"""
文件推送功能测试用例
测试将测试文件推送到Android设备的功能
"""
import pytest
import allure
import time
from tests.base.base_test import BaseTest
from tools.file_pusher import FilePusher
from core.logger import log


@allure.epic("工具测试")
@allure.feature("文件推送功能")
class TestFilePusher(BaseTest):
    """文件推送功能测试类"""

    def setup_method(self):
        """测试前准备"""
        self.file_pusher = FilePusher()

    @allure.story("设备连接检查")
    @allure.title("测试设备连接状态")
    @allure.description("检查Android设备是否正确连接")
    def test_device_connection(self):
        """测试设备连接"""
        with allure.step("检查设备连接状态"):
            result = self.file_pusher.check_device_connection()
            
        with allure.step("验证连接结果"):
            assert result, "设备未连接或ADB不可用"
            log.info("✅ 设备连接检查通过")

    @allure.story("文档推送功能")
    @allure.title("测试文档文件推送")
    @allure.description("测试将测试文档推送到设备Download目录")
    def test_push_documents(self):
        """测试推送文档"""
        target_path = "/sdcard/Download"
        
        with allure.step("检查设备连接"):
            assert self.file_pusher.check_device_connection(), "设备未连接"

        with allure.step("清理目标目录"):
            # 清理之前的测试文件
            self.file_pusher.clean_target_directory(target_path, "*.txt")
            self.file_pusher.clean_target_directory(target_path, "*.pdf")
            self.file_pusher.clean_target_directory(target_path, "*.doc*")

        with allure.step("推送文档文件"):
            result = self.file_pusher.push_documents_to_device(target_path)
            
        with allure.step("验证推送结果"):
            assert result, "文档推送失败"
            
            # 验证文件是否存在
            files = self.file_pusher.list_device_files(target_path)
            document_files = [f for f in files if any(f.endswith(ext) for ext in ['.txt', '.pdf', '.doc', '.docx'])]
            
            assert len(document_files) > 0, f"未找到推送的文档文件，目录内容: {files}"
            log.info(f"✅ 成功推送 {len(document_files)} 个文档文件: {document_files}")

    @allure.story("图片推送功能")
    @allure.title("测试图片文件推送")
    @allure.description("测试将测试图片推送到设备Pictures目录")
    def test_push_images(self):
        """测试推送图片"""
        target_path = "/sdcard/Pictures"
        
        with allure.step("检查设备连接"):
            assert self.file_pusher.check_device_connection(), "设备未连接"

        with allure.step("清理目标目录"):
            # 清理之前的测试文件
            self.file_pusher.clean_target_directory(target_path, "*.jpg")
            self.file_pusher.clean_target_directory(target_path, "*.png")
            self.file_pusher.clean_target_directory(target_path, "*.jpeg")

        with allure.step("推送图片文件"):
            result = self.file_pusher.push_images_to_device(target_path)
            
        with allure.step("验证推送结果"):
            assert result, "图片推送失败"
            
            # 验证文件是否存在
            files = self.file_pusher.list_device_files(target_path)
            image_files = [f for f in files if any(f.endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp'])]
            
            assert len(image_files) > 0, f"未找到推送的图片文件，目录内容: {files}"
            log.info(f"✅ 成功推送 {len(image_files)} 个图片文件: {image_files}")

    @allure.story("目录管理功能")
    @allure.title("测试目录清理功能")
    @allure.description("测试清理设备目录中的文件")
    def test_clean_directory(self):
        """测试目录清理功能"""
        target_path = "/sdcard/Download"
        
        with allure.step("检查设备连接"):
            assert self.file_pusher.check_device_connection(), "设备未连接"

        with allure.step("推送测试文件"):
            # 先推送一些文件
            self.file_pusher.push_documents_to_device(target_path)
            
            # 验证文件存在
            files_before = self.file_pusher.list_device_files(target_path)
            document_files_before = [f for f in files_before if f.endswith('.txt')]
            
            if len(document_files_before) == 0:
                pytest.skip("没有可清理的测试文件")

        with allure.step("清理目录"):
            result = self.file_pusher.clean_target_directory(target_path, "*.txt")
            
        with allure.step("验证清理结果"):
            assert result, "目录清理失败"
            
            # 验证文件是否被清理
            files_after = self.file_pusher.list_device_files(target_path)
            document_files_after = [f for f in files_after if f.endswith('.txt')]
            
            assert len(document_files_after) < len(document_files_before), "文件未被清理"
            log.info(f"✅ 成功清理文件，清理前: {len(document_files_before)}，清理后: {len(document_files_after)}")

    @allure.story("文件列表功能")
    @allure.title("测试设备文件列表")
    @allure.description("测试列出设备目录中的文件")
    def test_list_device_files(self):
        """测试列出设备文件"""
        target_path = "/sdcard/Download"
        
        with allure.step("检查设备连接"):
            assert self.file_pusher.check_device_connection(), "设备未连接"

        with allure.step("列出设备文件"):
            files = self.file_pusher.list_device_files(target_path)
            
        with allure.step("验证文件列表"):
            assert isinstance(files, list), "返回的文件列表格式不正确"
            log.info(f"✅ 成功列出 {len(files)} 个文件: {files[:10]}...")  # 只显示前10个

    @allure.story("综合测试")
    @allure.title("测试完整的文件推送流程")
    @allure.description("测试从推送到验证的完整流程")
    def test_complete_file_push_workflow(self):
        """测试完整的文件推送工作流程"""
        doc_target = "/sdcard/Download"
        img_target = "/sdcard/Pictures"
        
        with allure.step("检查设备连接"):
            assert self.file_pusher.check_device_connection(), "设备未连接"

        with allure.step("清理目标目录"):
            self.file_pusher.clean_target_directory(doc_target, "*.txt")
            self.file_pusher.clean_target_directory(img_target, "*.jpg")

        with allure.step("推送文档和图片"):
            doc_result = self.file_pusher.push_documents_to_device(doc_target)
            img_result = self.file_pusher.push_images_to_device(img_target)
            
        with allure.step("验证推送结果"):
            assert doc_result, "文档推送失败"
            assert img_result, "图片推送失败"
            
            # 验证文档
            doc_files = self.file_pusher.list_device_files(doc_target)
            doc_count = len([f for f in doc_files if f.endswith('.txt')])
            
            # 验证图片
            img_files = self.file_pusher.list_device_files(img_target)
            img_count = len([f for f in img_files if f.endswith('.jpg')])
            
            log.info(f"✅ 完整流程测试通过 - 文档: {doc_count}个, 图片: {img_count}个")

    @pytest.mark.parametrize("target_path", ["/sdcard/Download", "/sdcard/Pictures", "/sdcard/Documents"])
    @allure.story("参数化测试")
    @allure.title("测试不同目标路径的文件推送")
    @allure.description("使用不同的目标路径测试文件推送功能")
    def test_push_to_different_paths(self, target_path):
        """参数化测试不同路径的文件推送"""
        with allure.step(f"检查设备连接 - {target_path}"):
            assert self.file_pusher.check_device_connection(), "设备未连接"

        with allure.step(f"推送文档到 {target_path}"):
            result = self.file_pusher.push_documents_to_device(target_path)
            
        with allure.step(f"验证推送结果 - {target_path}"):
            assert result, f"推送到 {target_path} 失败"
            
            # 验证文件存在
            files = self.file_pusher.list_device_files(target_path)
            log.info(f"✅ 成功推送到 {target_path}，文件数: {len(files)}")

    def teardown_method(self):
        """测试后清理"""
        # 可选：清理测试文件
        if hasattr(self, 'file_pusher'):
            try:
                self.file_pusher.clean_target_directory("/sdcard/Download", "bcy_doc.txt")
                self.file_pusher.clean_target_directory("/sdcard/Pictures", "*.jpg")
            except Exception as e:
                log.warning(f"清理测试文件时出错: {e}")
