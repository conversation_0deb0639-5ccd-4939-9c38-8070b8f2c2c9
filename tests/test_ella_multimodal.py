"""
Ella多模态功能测试用例
测试文档、图库、相机、AI生图等多模态功能
"""
import pytest
import allure
import time
from tests.base.base_test import BaseTest
from core.logger import log


@allure.epic("Ella语音助手")
@allure.feature("多模态功能")
class TestEllaMultimodal(BaseTest):
    """Ella多模态功能测试类"""

    @allure.story("文档功能")
    @allure.title("测试文档选择功能")
    @allure.description("测试通过多模态入口选择文档的完整流程，包括文件推送")
    def test_document_function(self, ella_app):
        """测试文档功能"""
        with allure.step("确保在对话页面"):
            assert ella_app.ensure_on_chat_page(), "无法确保在对话页面"

        with allure.step("执行文档功能（包含文件推送）"):
            result = ella_app.execute_multimodal_function("document")

        with allure.step("验证功能执行结果"):
            assert result, "文档功能执行失败"
            log.info("✅ 文档功能测试通过（包含文件推送）")

    @allure.story("图库功能")
    @allure.title("测试图库选择功能")
    @allure.description("测试通过多模态入口选择图片的完整流程，包括图片推送")
    def test_gallery_function(self, ella_app):
        """测试图库功能"""
        with allure.step("确保在对话页面"):
            assert ella_app.ensure_on_chat_page(), "无法确保在对话页面"

        with allure.step("执行图库功能（包含图片推送）"):
            result = ella_app.execute_multimodal_function("gallery")

        with allure.step("验证功能执行结果"):
            assert result, "图库功能执行失败"
            log.info("✅ 图库功能测试通过（包含图片推送）")

    @allure.story("相机功能")
    @allure.title("测试相机拍照功能")
    @allure.description("测试通过多模态入口进行拍照的完整流程")
    def test_camera_function(self, ella_app):
        """测试相机功能"""
        with allure.step("确保在对话页面"):
            assert ella_app.ensure_on_chat_page(), "无法确保在对话页面"

        with allure.step("执行相机功能"):
            result = ella_app.execute_multimodal_function("camera")
            
        with allure.step("验证功能执行结果"):
            assert result, "相机功能执行失败"
            log.info("✅ 相机功能测试通过")

    @allure.story("AI生图功能")
    @allure.title("测试AI图像生成功能")
    @allure.description("测试通过多模态入口使用AI生成图像的完整流程")
    def test_ai_image_generator_function(self, ella_app):
        """测试AI生图功能"""
        with allure.step("确保在对话页面"):
            assert ella_app.ensure_on_chat_page(), "无法确保在对话页面"

        with allure.step("执行AI生图功能"):
            result = ella_app.execute_multimodal_function("ai_image_generator")
            
        with allure.step("验证功能执行结果"):
            assert result, "AI生图功能执行失败"
            log.info("✅ AI生图功能测试通过")

    @allure.story("多模态功能综合测试")
    @allure.title("测试所有多模态功能")
    @allure.description("依次测试所有多模态功能，验证功能的完整性和稳定性")
    def test_all_multimodal_functions(self, ella_app):
        """测试所有多模态功能"""
        functions = ["document", "gallery", "camera", "ai_image_generator"]
        results = {}
        
        for function in functions:
            with allure.step(f"测试{function}功能"):
                # 确保在对话页面
                assert ella_app.ensure_on_chat_page(), f"测试{function}前无法确保在对话页面"
                
                # 执行功能
                result = ella_app.execute_multimodal_function(function)
                results[function] = result
                
                # 记录结果
                if result:
                    log.info(f"✅ {function}功能测试成功")
                else:
                    log.error(f"❌ {function}功能测试失败")
                
                # 功能间间隔
                time.sleep(2)
        
        with allure.step("验证所有功能执行结果"):
            failed_functions = [func for func, result in results.items() if not result]
            
            if failed_functions:
                log.error(f"以下功能测试失败: {failed_functions}")
                pytest.fail(f"多模态功能测试失败: {failed_functions}")
            else:
                log.info("✅ 所有多模态功能测试通过")

    @allure.story("多模态功能错误处理")
    @allure.title("测试不支持的模型类型")
    @allure.description("测试传入不支持的模型类型时的错误处理")
    def test_unsupported_model_type(self, ella_app):
        """测试不支持的模型类型"""
        with allure.step("确保在对话页面"):
            assert ella_app.ensure_on_chat_page(), "无法确保在对话页面"

        with allure.step("执行不支持的模型类型"):
            result = ella_app.execute_multimodal_function("unsupported_model")
            
        with allure.step("验证错误处理"):
            assert not result, "应该返回False表示不支持的模型类型"
            log.info("✅ 不支持模型类型的错误处理正确")

    @allure.story("多模态功能稳定性测试")
    @allure.title("测试多模态功能的重复执行")
    @allure.description("重复执行多模态功能，验证功能的稳定性")
    def test_multimodal_function_stability(self, ella_app):
        """测试多模态功能稳定性"""
        function = "document"  # 选择一个相对稳定的功能进行重复测试
        repeat_count = 3
        
        for i in range(repeat_count):
            with allure.step(f"第{i+1}次执行{function}功能"):
                # 确保在对话页面
                assert ella_app.ensure_on_chat_page(), f"第{i+1}次测试前无法确保在对话页面"
                
                # 执行功能
                result = ella_app.execute_multimodal_function(function)
                
                # 验证结果
                assert result, f"第{i+1}次{function}功能执行失败"
                log.info(f"✅ 第{i+1}次{function}功能测试成功")
                
                # 间隔时间
                if i < repeat_count - 1:
                    time.sleep(3)
        
        log.info(f"✅ {function}功能稳定性测试通过，连续{repeat_count}次执行成功")

    @pytest.mark.parametrize("model_type", ["document", "gallery", "camera", "ai_image_generator"])
    @allure.story("参数化多模态功能测试")
    @allure.title("参数化测试各种多模态功能")
    @allure.description("使用参数化测试分别验证每种多模态功能")
    def test_multimodal_function_parametrized(self, ella_app, model_type):
        """参数化测试多模态功能"""
        with allure.step(f"确保在对话页面 - {model_type}"):
            assert ella_app.ensure_on_chat_page(), "无法确保在对话页面"

        with allure.step(f"执行{model_type}功能"):
            result = ella_app.execute_multimodal_function(model_type)
            
        with allure.step(f"验证{model_type}功能执行结果"):
            assert result, f"{model_type}功能执行失败"
            log.info(f"✅ {model_type}功能参数化测试通过")

    @allure.story("多模态入口测试")
    @allure.title("测试多模态入口按钮")
    @allure.description("单独测试多模态入口按钮的点击功能")
    def test_multimodal_entrance_button(self, ella_app):
        """测试多模态入口按钮"""
        with allure.step("确保在对话页面"):
            assert ella_app.ensure_on_chat_page(), "无法确保在对话页面"

        with allure.step("测试多模态入口按钮点击"):
            # 直接调用多模态处理器的入口方法
            result = ella_app.multimodal_handler._open_multimodal_entrance()

        with allure.step("验证入口按钮功能"):
            assert result, "多模态入口按钮点击失败"
            log.info("✅ 多模态入口按钮测试通过")
