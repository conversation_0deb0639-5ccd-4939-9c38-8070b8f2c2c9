"""
calculator Detector检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class CalculatorDetector(BaseAppDetector):
    """计算器应用检测器"""

    def __init__(self):
        super().__init__(AppType.CALCULATOR)

    def get_package_names(self) -> List[str]:
        """获取计算器应用包名列表"""
        return [
            "com.transsion.calculator"
        ]

    def get_keywords(self) -> List[str]:
        """获取计算器应用关键词列表"""
        return ["calculator", "计算器"]
