"""
My Health应用Work Outs页面
参考Ella的dialogue_page实现，专注于页面元素定义和基本页面操作
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from pages.base.common_page import CommonPage
from pages.base.system_status_checker import SystemStatusChecker
from pages.base.app_detector import AppDetector, AppType
from tools.adb_process_monitor import AdbProcessMonitor
from core.logger import log


class MyHealthWorkOutsPage(CommonPage):
    """My Health应用Work Outs页面"""

    def __init__(self):
        """初始化Work Outs页面"""
        super().__init__("my_health", "work_outs_page")

        # 初始化页面元素
        self._init_elements()

        # 初始化功能模块
        self._init_modules()

    def _init_elements(self):
        """初始化页面元素 - 基于My Health Work Outs页面的实际元素"""
        # 应用包名验证
        self.app_package = self.create_element(
            {"packageName": "com.transsion.healthlife"},
            "My Health应用包"
        )

        # 页面标题
        self.work_outs_title = self.create_element(
            {"text": "Work Outs"},
            "Work Outs页面标题"
        )

        self.work_outs_title_cn = self.create_element(
            {"text": "锻炼"},
            "锻炼页面标题(中文)"
        )

        # 搜索框
        self.search_box = self.create_element(
            {"resourceId": "com.transsion.healthlife:id/search_edit_text"},
            "搜索框"
        )

        self.search_box_alt = self.create_element(
            {"className": "android.widget.EditText", "hint": "搜索锻炼"},
            "搜索框(备选)"
        )

        # 搜索按钮
        self.search_button = self.create_element(
            {"resourceId": "com.transsion.healthlife:id/search_button"},
            "搜索按钮"
        )

        # 锻炼分类标签
        self.category_tabs = self.create_element(
            {"className": "android.widget.HorizontalScrollView"},
            "锻炼分类标签容器"
        )

        # 全部分类
        self.all_category = self.create_element(
            {"text": "All"},
            "全部分类"
        )

        self.all_category_cn = self.create_element(
            {"text": "全部"},
            "全部分类(中文)"
        )

        # 有氧运动分类
        self.cardio_category = self.create_element(
            {"text": "Cardio"},
            "有氧运动分类"
        )

        self.cardio_category_cn = self.create_element(
            {"text": "有氧运动"},
            "有氧运动分类(中文)"
        )

        # 力量训练分类
        self.strength_category = self.create_element(
            {"text": "Strength"},
            "力量训练分类"
        )

        self.strength_category_cn = self.create_element(
            {"text": "力量训练"},
            "力量训练分类(中文)"
        )

        # 瑜伽分类
        self.yoga_category = self.create_element(
            {"text": "Yoga"},
            "瑜伽分类"
        )

        self.yoga_category_cn = self.create_element(
            {"text": "瑜伽"},
            "瑜伽分类(中文)"
        )

        # 锻炼列表
        self.workout_list = self.create_element(
            {"className": "androidx.recyclerview.widget.RecyclerView"},
            "锻炼列表"
        )

        self.workout_list_alt = self.create_element(
            {"resourceId": "com.transsion.healthlife:id/workout_recycler_view"},
            "锻炼列表(备选)"
        )

        # 锻炼卡片
        self.workout_card = self.create_element(
            {"className": "android.widget.CardView"},
            "锻炼卡片"
        )

        # 开始锻炼按钮
        self.start_workout_button = self.create_element(
            {"text": "Start Workout"},
            "开始锻炼按钮"
        )

        self.start_workout_button_cn = self.create_element(
            {"text": "开始锻炼"},
            "开始锻炼按钮(中文)"
        )

        # 收藏按钮
        self.favorite_button = self.create_element(
            {"resourceId": "com.transsion.healthlife:id/favorite_button"},
            "收藏按钮"
        )

        # 分享按钮
        self.share_button = self.create_element(
            {"resourceId": "com.transsion.healthlife:id/share_button"},
            "分享按钮"
        )

        # 筛选按钮
        self.filter_button = self.create_element(
            {"resourceId": "com.transsion.healthlife:id/filter_button"},
            "筛选按钮"
        )

        self.filter_button_alt = self.create_element(
            {"text": "Filter"},
            "筛选按钮(备选)"
        )

        # 排序按钮
        self.sort_button = self.create_element(
            {"resourceId": "com.transsion.healthlife:id/sort_button"},
            "排序按钮"
        )

        # 底部导航栏
        self.bottom_navigation = self.create_element(
            {"className": "com.google.android.material.bottomnavigation.BottomNavigationView"},
            "底部导航栏"
        )

        # 主页按钮
        self.home_button = self.create_element(
            {"text": "Home"},
            "主页按钮"
        )

        self.home_button_cn = self.create_element(
            {"text": "主页"},
            "主页按钮(中文)"
        )

        # 返回按钮
        self.back_button = self.create_element(
            {"className": "android.widget.ImageButton", "description": "Navigate up"},
            "返回按钮"
        )

        # 更多选项按钮
        self.more_options_button = self.create_element(
            {"className": "android.widget.ImageView", "description": "More options"},
            "更多选项按钮"
        )

        # 刷新按钮
        self.refresh_button = self.create_element(
            {"resourceId": "com.transsion.healthlife:id/refresh_button"},
            "刷新按钮"
        )

        # 空状态视图
        self.empty_state_view = self.create_element(
            {"resourceId": "com.transsion.healthlife:id/empty_state_layout"},
            "空状态视图"
        )

        # 加载指示器
        self.loading_indicator = self.create_element(
            {"className": "android.widget.ProgressBar"},
            "加载指示器"
        )

        # 错误提示
        self.error_message = self.create_element(
            {"resourceId": "com.transsion.healthlife:id/error_message"},
            "错误提示"
        )

        # 网络错误提示
        self.network_error = self.create_element(
            {"text": "Network error"},
            "网络错误提示"
        )

        self.network_error_cn = self.create_element(
            {"text": "网络错误"},
            "网络错误提示(中文)"
        )

    def _init_modules(self):
        """初始化功能模块"""
        # 页面元素字典，供其他模块使用
        self.page_elements = {
            'search_box': self.search_box,
            'search_box_alt': self.search_box_alt,
            'search_button': self.search_button,
            'category_tabs': self.category_tabs,
            'workout_list': self.workout_list,
            'workout_list_alt': self.workout_list_alt,
            'start_workout_button': self.start_workout_button,
            'start_workout_button_cn': self.start_workout_button_cn,
            'favorite_button': self.favorite_button,
            'share_button': self.share_button,
            'filter_button': self.filter_button,
            'sort_button': self.sort_button,
            'bottom_navigation': self.bottom_navigation,
            'home_button': self.home_button,
            'back_button': self.back_button
        }

        # 初始化功能模块
        self.status_checker = SystemStatusChecker(self.driver)
        self.app_detector = AppDetector()
        self.process_monitor = AdbProcessMonitor()

    # ==================== 应用启动和页面管理 ====================

    def start_app(self) -> bool:
        """启动My Health应用"""
        try:
            log.info("启动My Health应用")

            package_name = "com.transsion.healthlife"
            activity_name = "com.transsion.healthlife.MainActivity"

            # 方法1: 尝试启动指定Activity
            try:
                self.driver.app_start(package_name, activity_name)
                log.info(f"尝试启动Activity: {activity_name}")
                time.sleep(3)

                # 检查应用是否启动成功
                if self._check_app_started(package_name):
                    log.info("✅ My Health应用启动成功（指定Activity）")
                    return True
            except Exception as e:
                log.warning(f"指定Activity启动失败: {e}")

            # 方法2: 备选方案：使用默认启动方式
            try:
                self.driver.app_start(package_name)
                log.info("尝试默认方式启动应用")
                time.sleep(3)

                if self._check_app_started(package_name):
                    log.info("✅ My Health应用启动成功（默认方式）")
                    return True
            except Exception as e:
                log.warning(f"默认启动方式失败: {e}")

            # 方法3: 通过ADB命令启动
            try:
                log.info("尝试通过ADB命令启动应用")
                import subprocess
                result = subprocess.run([
                    "adb", "shell", "am", "start", 
                    "-n", f"{package_name}/{activity_name}"
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    time.sleep(3)
                    if self._check_app_started(package_name):
                        log.info("✅ My Health应用启动成功（ADB命令）")
                        return True
            except Exception as e:
                log.warning(f"ADB命令启动失败: {e}")

            log.error("❌ 所有启动方法都失败")
            return False

        except Exception as e:
            log.error(f"启动My Health应用异常: {e}")
            return False

    def _check_app_started(self, package_name: str) -> bool:
        """检查应用是否启动成功"""
        try:
            # 检查当前应用包名
            current_app = self.driver.app_current()
            if current_app and current_app.get('package') == package_name:
                log.info(f"✅ 应用已启动: {package_name}")
                return True
            
            # 检查应用包元素是否存在
            if self.app_package.is_exists():
                log.info("✅ 应用包元素检测成功")
                return True
                
            log.warning(f"❌ 应用未启动或启动失败: {package_name}")
            return False
            
        except Exception as e:
            log.warning(f"检查应用启动状态异常: {e}")
            return False

    def wait_for_page_load(self, timeout: int = 15) -> bool:
        """等待页面加载完成"""
        try:
            log.info(f"等待Work Outs页面加载完成 (超时: {timeout}秒)")

            # 等待页面标题出现
            if (self.work_outs_title.wait_for_element(timeout=timeout) or
                self.work_outs_title_cn.wait_for_element(timeout=5)):
                log.info("✅ 页面标题已出现，页面加载完成")
                return True
            elif (self.workout_list.wait_for_element(timeout=5) or
                  self.workout_list_alt.wait_for_element(timeout=5)):
                log.info("✅ 锻炼列表已出现，页面加载完成")
                return True
            elif self.search_box.wait_for_element(timeout=5):
                log.info("✅ 搜索框已出现，页面加载完成")
                return True
            else:
                log.error("❌ 页面加载超时，未找到关键元素")
                return False

        except Exception as e:
            log.error(f"等待页面加载异常: {e}")
            return False

    def stop_app(self) -> bool:
        """停止My Health应用"""
        try:
            log.info("停止My Health应用")
            package_name = "com.transsion.healthlife"

            # app_stop方法不返回值，直接执行操作
            self.driver.app_stop(package_name)

            # 等待一下让应用完全停止
            time.sleep(1)

            # 验证应用是否真的停止了
            if self._verify_app_stopped(package_name):
                log.info("✅ My Health应用已成功停止")
                return True
            else:
                log.info("✅ My Health应用停止命令已执行")
                return True  # 即使验证失败，也认为停止命令执行成功

        except Exception as e:
            log.error(f"停止My Health应用异常: {e}")
            return False

    def _verify_app_stopped(self, package_name: str) -> bool:
        """验证应用是否已停止"""
        try:
            current_app = self.driver.app_current()
            if current_app and current_app.get('package') == package_name:
                log.warning(f"应用仍在运行: {package_name}")
                return False
            else:
                log.info(f"应用已停止: {package_name}")
                return True
        except Exception as e:
            log.warning(f"验证应用停止状态异常: {e}")
            return False

    def navigate_to_work_outs(self) -> bool:
        """导航到Work Outs页面"""
        try:
            log.info("导航到Work Outs页面")

            # 方法1: 通过底部导航栏
            if self.bottom_navigation.is_exists():
                log.info("通过底部导航栏进入Work Outs页面")
                # 查找Work Outs导航项
                work_outs_nav = self.driver(text="Work Outs")
                if not work_outs_nav.exists():
                    work_outs_nav = self.driver(text="锻炼")

                if work_outs_nav.exists():
                    if work_outs_nav.click():
                        # 等待页面加载
                        if self.wait_for_page_load(timeout=5):
                            log.info("✅ 成功导航到Work Outs页面")
                            return True
                        else:
                            log.warning("导航后页面加载失败")

            # 方法2: 通过滑动手势（如果是滑动切换页面）
            log.info("尝试通过滑动手势进入Work Outs页面")
            screen_width, screen_height = self.driver.window_size()

            # 从右向左滑动（假设Work Outs页面在主页右侧）
            start_x = int(screen_width * 0.8)
            end_x = int(screen_width * 0.2)
            y = int(screen_height * 0.5)

            self.driver.swipe(start_x, y, end_x, y, duration=0.5)
            time.sleep(1)

            # 检查是否成功进入Work Outs页面
            if self.wait_for_page_load(timeout=3):
                log.info("✅ 通过滑动成功导航到Work Outs页面")
                return True

            log.error("❌ 无法导航到Work Outs页面")
            return False

        except Exception as e:
            log.error(f"导航到Work Outs页面异常: {e}")
            return False

    def navigate_to_home(self) -> bool:
        """返回主页"""
        try:
            log.info("返回主页")

            # 方法1: 点击主页按钮
            if self.home_button.is_exists():
                if self.home_button.click():
                    log.info("✅ 通过主页按钮返回主页")
                    return True
            elif self.home_button_cn.is_exists():
                if self.home_button_cn.click():
                    log.info("✅ 通过主页按钮返回主页(中文)")
                    return True

            # 方法2: 点击返回按钮
            if self.back_button.is_exists():
                if self.back_button.click():
                    log.info("✅ 通过返回按钮返回主页")
                    return True

            # 方法3: 按返回键
            self.driver.press("back")
            time.sleep(1)
            log.info("✅ 通过返回键返回主页")
            return True

        except Exception as e:
            log.error(f"返回主页异常: {e}")
            return False

    # ==================== 搜索功能 ====================

    def search_workout(self, query: str) -> bool:
        """搜索锻炼"""
        try:
            log.info(f"搜索锻炼: {query}")

            # 确保在Work Outs页面
            if not self.wait_for_page_load(timeout=3):
                log.error("不在Work Outs页面，无法执行搜索")
                return False

            # 点击搜索框
            search_element = None
            if self.search_box.is_exists():
                search_element = self.search_box
            elif self.search_box_alt.is_exists():
                search_element = self.search_box_alt

            if search_element:
                if search_element.click():
                    log.info("✅ 搜索框点击成功")
                    time.sleep(0.5)

                    # 清空搜索框并输入查询内容
                    search_element.clear_text()
                    search_element.send_keys(query)
                    log.info(f"✅ 输入搜索内容: {query}")

                    # 点击搜索按钮或按回车
                    if self.search_button.is_exists():
                        self.search_button.click()
                    else:
                        self.driver.press("enter")

                    time.sleep(2)  # 等待搜索结果
                    log.info("✅ 搜索执行完成")
                    return True
                else:
                    log.error("搜索框点击失败")
                    return False
            else:
                log.error("未找到搜索框")
                return False

        except Exception as e:
            log.error(f"搜索锻炼失败: {e}")
            return False

    def clear_search(self) -> bool:
        """清空搜索"""
        try:
            log.info("清空搜索")

            search_element = None
            if self.search_box.is_exists():
                search_element = self.search_box
            elif self.search_box_alt.is_exists():
                search_element = self.search_box_alt

            if search_element:
                search_element.clear_text()
                log.info("✅ 搜索已清空")
                return True
            else:
                log.warning("未找到搜索框")
                return False

        except Exception as e:
            log.error(f"清空搜索失败: {e}")
            return False

    # ==================== 分类功能 ====================

    def select_category(self, category: str) -> bool:
        """选择锻炼分类"""
        try:
            log.info(f"选择锻炼分类: {category}")

            # 分类映射
            category_mapping = {
                "all": [self.all_category, self.all_category_cn],
                "cardio": [self.cardio_category, self.cardio_category_cn],
                "strength": [self.strength_category, self.strength_category_cn],
                "yoga": [self.yoga_category, self.yoga_category_cn]
            }

            category_lower = category.lower()
            if category_lower in category_mapping:
                for category_element in category_mapping[category_lower]:
                    if category_element.is_exists():
                        if category_element.click():
                            log.info(f"✅ 成功选择分类: {category}")
                            time.sleep(1)  # 等待分类内容加载
                            return True
                        break

            # 如果预定义分类不存在，尝试直接通过文本查找
            category_element = self.driver(text=category)
            if category_element.exists():
                if category_element.click():
                    log.info(f"✅ 成功选择分类: {category}")
                    time.sleep(1)
                    return True

            log.error(f"❌ 未找到分类: {category}")
            return False

        except Exception as e:
            log.error(f"选择分类失败: {e}")
            return False

    def get_available_categories(self) -> list:
        """获取可用的锻炼分类"""
        try:
            log.info("获取可用的锻炼分类")
            categories = []

            # 检查预定义分类
            category_elements = [
                (self.all_category, "All"),
                (self.all_category_cn, "全部"),
                (self.cardio_category, "Cardio"),
                (self.cardio_category_cn, "有氧运动"),
                (self.strength_category, "Strength"),
                (self.strength_category_cn, "力量训练"),
                (self.yoga_category, "Yoga"),
                (self.yoga_category_cn, "瑜伽")
            ]

            for element, name in category_elements:
                if element.is_exists():
                    categories.append(name)

            # 如果有分类标签容器，尝试获取所有分类
            if self.category_tabs.is_exists():
                try:
                    # 获取分类容器中的所有文本元素
                    tab_elements = self.driver(className="android.widget.TextView")
                    for element in tab_elements:
                        if element.exists():
                            text = element.get_text()
                            if text and text not in categories:
                                categories.append(text)
                except Exception as e:
                    log.warning(f"获取分类标签失败: {e}")

            log.info(f"✅ 找到 {len(categories)} 个分类: {categories}")
            return categories

        except Exception as e:
            log.error(f"获取分类失败: {e}")
            return []

    # ==================== 锻炼列表操作 ====================

    def get_workout_count(self) -> int:
        """获取锻炼数量"""
        try:
            log.info("获取锻炼数量")

            # 方法1: 通过RecyclerView获取
            if self.workout_list.is_exists():
                try:
                    # 获取RecyclerView中的子元素数量
                    workout_items = self.driver(className="android.widget.CardView")
                    count = len(workout_items)
                    log.info(f"✅ 通过CardView找到 {count} 个锻炼")
                    return count
                except Exception as e:
                    log.warning(f"通过CardView获取数量失败: {e}")

            # 方法2: 通过备选列表获取
            if self.workout_list_alt.is_exists():
                try:
                    workout_items = self.workout_list_alt.child(className="android.view.ViewGroup")
                    count = len(workout_items)
                    log.info(f"✅ 通过备选列表找到 {count} 个锻炼")
                    return count
                except Exception as e:
                    log.warning(f"通过备选列表获取数量失败: {e}")

            # 方法3: 通过文本元素计数
            try:
                workout_titles = self.driver(className="android.widget.TextView")
                count = 0
                for title in workout_titles:
                    if title.exists():
                        text = title.get_text()
                        # 简单判断是否为锻炼标题（包含常见锻炼关键词）
                        if text and any(keyword in text.lower() for keyword in
                                      ["workout", "exercise", "training", "锻炼", "运动", "训练"]):
                            count += 1
                log.info(f"✅ 通过文本元素找到 {count} 个锻炼")
                return count
            except Exception as e:
                log.warning(f"通过文本元素计数失败: {e}")

            log.warning("❌ 无法获取锻炼数量")
            return 0

        except Exception as e:
            log.error(f"获取锻炼数量失败: {e}")
            return 0

    def get_workout_list(self) -> list:
        """获取锻炼列表"""
        try:
            log.info("获取锻炼列表")
            workouts = []

            # 获取所有锻炼卡片
            workout_cards = self.driver(className="android.widget.CardView")

            for i, card in enumerate(workout_cards):
                if card.exists():
                    try:
                        # 获取锻炼标题
                        title_element = card.child(className="android.widget.TextView")
                        title = title_element.get_text() if title_element.exists() else f"Workout {i+1}"

                        # 获取锻炼信息
                        workout_info = {
                            "index": i,
                            "title": title,
                            "element": card
                        }

                        workouts.append(workout_info)

                    except Exception as e:
                        log.warning(f"获取第 {i+1} 个锻炼信息失败: {e}")

            log.info(f"✅ 获取到 {len(workouts)} 个锻炼")
            return workouts

        except Exception as e:
            log.error(f"获取锻炼列表失败: {e}")
            return []

    def select_workout(self, workout_index: int = 0) -> bool:
        """选择锻炼"""
        try:
            log.info(f"选择第 {workout_index + 1} 个锻炼")

            workouts = self.get_workout_list()
            if not workouts:
                log.error("❌ 没有找到锻炼列表")
                return False

            if workout_index >= len(workouts):
                log.error(f"❌ 锻炼索引超出范围: {workout_index}, 总数: {len(workouts)}")
                return False

            workout = workouts[workout_index]
            if workout["element"].click():
                log.info(f"✅ 成功选择锻炼: {workout['title']}")
                time.sleep(1)  # 等待锻炼详情页面加载
                return True
            else:
                log.error(f"❌ 点击锻炼失败: {workout['title']}")
                return False

        except Exception as e:
            log.error(f"选择锻炼失败: {e}")
            return False

    def start_workout(self, workout_index: int = 0) -> bool:
        """开始锻炼"""
        try:
            log.info(f"开始第 {workout_index + 1} 个锻炼")

            # 首先选择锻炼
            if not self.select_workout(workout_index):
                return False

            # 查找并点击开始锻炼按钮
            start_buttons = [self.start_workout_button, self.start_workout_button_cn]

            for button in start_buttons:
                if button.is_exists():
                    if button.click():
                        log.info("✅ 成功开始锻炼")
                        time.sleep(2)  # 等待锻炼开始
                        return True
                    break

            # 如果预定义按钮不存在，尝试查找其他开始按钮
            start_button = self.driver(textContains="Start")
            if not start_button.exists():
                start_button = self.driver(textContains="开始")

            if start_button.exists():
                if start_button.click():
                    log.info("✅ 成功开始锻炼")
                    time.sleep(2)
                    return True

            log.error("❌ 未找到开始锻炼按钮")
            return False

        except Exception as e:
            log.error(f"开始锻炼失败: {e}")
            return False

    # ==================== 页面交互功能 ====================

    def scroll_to_top(self) -> bool:
        """滚动到页面顶部"""
        try:
            log.info("滚动到页面顶部")

            if self.workout_list.is_exists():
                # 使用fling方法快速滚动到顶部
                self.workout_list.fling.toBeginning()
                time.sleep(1)
                log.info("✅ 已滚动到页面顶部")
                return True
            else:
                # 备选方案：使用滑动手势
                screen_width, screen_height = self.driver.window_size()
                start_x = int(screen_width * 0.5)
                start_y = int(screen_height * 0.3)
                end_y = int(screen_height * 0.8)

                self.driver.swipe(start_x, start_y, start_x, end_y, duration=0.5)
                time.sleep(1)
                log.info("✅ 已滚动到页面顶部（备选方案）")
                return True

        except Exception as e:
            log.error(f"滚动到顶部失败: {e}")
            return False

    def scroll_to_bottom(self) -> bool:
        """滚动到页面底部"""
        try:
            log.info("滚动到页面底部")

            if self.workout_list.is_exists():
                # 使用fling方法快速滚动到底部
                self.workout_list.fling.toEnd()
                time.sleep(1)
                log.info("✅ 已滚动到页面底部")
                return True
            else:
                # 备选方案：使用滑动手势
                screen_width, screen_height = self.driver.window_size()
                start_x = int(screen_width * 0.5)
                start_y = int(screen_height * 0.8)
                end_y = int(screen_height * 0.3)

                self.driver.swipe(start_x, start_y, start_x, end_y, duration=0.5)
                time.sleep(1)
                log.info("✅ 已滚动到页面底部（备选方案）")
                return True

        except Exception as e:
            log.error(f"滚动到底部失败: {e}")
            return False

    def refresh_page(self) -> bool:
        """刷新页面"""
        try:
            log.info("刷新页面")

            # 方法1: 点击刷新按钮
            if self.refresh_button.is_exists():
                if self.refresh_button.click():
                    log.info("✅ 通过刷新按钮刷新页面")
                    time.sleep(2)  # 等待刷新完成
                    return True

            # 方法2: 下拉刷新
            if self.workout_list.is_exists():
                # 在列表顶部执行下拉手势
                screen_width, screen_height = self.driver.window_size()
                start_x = int(screen_width * 0.5)
                start_y = int(screen_height * 0.2)
                end_y = int(screen_height * 0.6)

                self.driver.swipe(start_x, start_y, start_x, end_y, duration=1.0)
                log.info("✅ 通过下拉手势刷新页面")
                time.sleep(2)
                return True

            log.warning("❌ 无法执行刷新操作")
            return False

        except Exception as e:
            log.error(f"刷新页面失败: {e}")
            return False

    def check_empty_state(self) -> bool:
        """检查是否显示空状态"""
        try:
            if self.empty_state_view.is_exists():
                log.info("✅ 检测到空状态视图")
                return True

            # 检查是否有"无锻炼"相关的文本
            empty_texts = ["No workouts", "没有锻炼", "暂无数据", "Empty"]
            for text in empty_texts:
                if self.driver(textContains=text).exists():
                    log.info(f"✅ 检测到空状态文本: {text}")
                    return True

            return False

        except Exception as e:
            log.error(f"检查空状态失败: {e}")
            return False

    def check_loading_state(self) -> bool:
        """检查是否正在加载"""
        try:
            if self.loading_indicator.is_exists():
                log.info("✅ 检测到加载指示器")
                return True

            # 检查是否有"加载中"相关的文本
            loading_texts = ["Loading", "加载中", "请稍候"]
            for text in loading_texts:
                if self.driver(textContains=text).exists():
                    log.info(f"✅ 检测到加载文本: {text}")
                    return True

            return False

        except Exception as e:
            log.error(f"检查加载状态失败: {e}")
            return False

    def handle_network_error(self) -> bool:
        """处理网络错误"""
        try:
            # 检查是否有网络错误提示
            if (self.network_error.is_exists() or
                self.network_error_cn.is_exists() or
                self.error_message.is_exists()):

                log.warning("检测到网络错误，尝试重试")

                # 尝试点击重试按钮
                retry_button = self.driver(text="Retry")
                if not retry_button.exists():
                    retry_button = self.driver(text="重试")

                if retry_button.exists():
                    retry_button.click()
                    log.info("✅ 点击重试按钮")
                    time.sleep(2)
                    return True
                else:
                    # 如果没有重试按钮，尝试刷新页面
                    return self.refresh_page()

            return True  # 没有网络错误

        except Exception as e:
            log.error(f"处理网络错误失败: {e}")
            return False

    # ==================== 页面状态检查 ====================

    def ensure_on_work_outs_page(self) -> bool:
        """确保当前在Work Outs页面"""
        try:
            log.info("确保在Work Outs页面...")

            # 检查当前进程是否是My Health
            if not self.status_checker.ensure_app_process("com.transsion.healthlife"):
                log.warning("当前不在My Health进程，尝试返回应用")
                if not self.start_app():
                    log.error("无法返回My Health应用")
                    return False

            # 检查是否在Work Outs页面
            if self._check_work_outs_page_indicators():
                log.info("✅ 已在Work Outs页面")
                return True

            # 尝试导航到Work Outs页面
            if self.navigate_to_work_outs():
                log.info("✅ 成功导航到Work Outs页面")
                return True

            log.error("❌ 无法确保在Work Outs页面")
            return False

        except Exception as e:
            log.error(f"确保在Work Outs页面失败: {e}")
            return False

    def _check_work_outs_page_indicators(self) -> bool:
        """检查Work Outs页面的指示器"""
        try:
            log.debug("检查Work Outs页面指示器...")

            # 指示器1: 页面标题存在
            if (self.work_outs_title.is_exists() or
                self.work_outs_title_cn.is_exists()):
                log.debug("✅ 找到页面标题")
                return True

            # 指示器2: 锻炼列表存在
            if (self.workout_list.is_exists() or
                self.workout_list_alt.is_exists()):
                log.debug("✅ 找到锻炼列表")
                return True

            # 指示器3: 搜索框存在
            if (self.search_box.is_exists() or
                self.search_box_alt.is_exists()):
                log.debug("✅ 找到搜索框")
                return True

            # 指示器4: 分类标签存在
            if self.category_tabs.is_exists():
                log.debug("✅ 找到分类标签")
                return True

            log.debug("❌ 未找到任何Work Outs页面指示器")
            return False

        except Exception as e:
            log.error(f"检查Work Outs页面指示器失败: {e}")
            return False

    # ==================== 应用检测方法 ====================

    def check_my_health_app_opened(self) -> bool:
        """检查My Health应用是否打开"""
        return self.app_detector.check_app_opened(AppType.HEALTHLIFE)

    # ==================== 综合测试方法 ====================

    def perform_comprehensive_test(self) -> bool:
        """执行Work Outs页面综合测试"""
        try:
            log.info("开始Work Outs页面综合测试")

            # 1. 启动应用
            if not self.start_app():
                log.error("❌ 应用启动失败")
                return False

            # 2. 等待页面加载
            if not self.wait_for_page_load():
                log.error("❌ 页面加载失败")
                return False

            # 3. 导航到Work Outs页面
            if not self.navigate_to_work_outs():
                log.error("❌ 导航到Work Outs页面失败")
                return False

            # 4. 测试搜索功能
            if not self.search_workout("yoga"):
                log.warning("⚠️ 搜索功能测试失败")

            # 5. 测试分类功能
            if not self.select_category("all"):
                log.warning("⚠️ 分类功能测试失败")

            # 6. 获取锻炼列表
            workouts = self.get_workout_list()
            if not workouts:
                log.warning("⚠️ 获取锻炼列表失败")

            # 7. 测试滚动功能
            self.scroll_to_bottom()
            self.scroll_to_top()

            # 8. 测试刷新功能
            self.refresh_page()

            log.info("✅ Work Outs页面综合测试完成")
            return True

        except Exception as e:
            log.error(f"综合测试失败: {e}")
            return False


if __name__ == '__main__':
    work_outs_page = MyHealthWorkOutsPage()
    result = work_outs_page.perform_comprehensive_test()
    print(f"测试结果: {result}")
