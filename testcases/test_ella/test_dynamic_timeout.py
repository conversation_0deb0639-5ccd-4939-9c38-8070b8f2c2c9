"""
Ella动态等待时间测试用例
测试根据不同类型指令设置不同等待时间的逻辑
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


class TestEllaDynamicTimeout(SimpleEllaTest):
    """Ella动态等待时间测试类"""

    @allure.feature("Ella动态等待时间")
    @allure.story("多模态命令超时测试")
    @allure.title("测试多模态命令的动态超时时间")
    def test_multimodal_command_timeout(self, ella_app):
        """
        测试多模态命令的动态超时时间设置
        
        Args:
            ella_app: Ella应用实例
        """
        # 测试不同多模态类型的超时时间
        multimodal_test_cases = [
            {
                "command": "help me select a document",
                "multimodal_type": "document",
                "expected_timeout": 12,
                "description": "文档选择命令"
            },
            {
                "command": "choose a photo from gallery",
                "multimodal_type": "gallery",
                "expected_timeout": 10,
                "description": "图库选择命令"
            },
            {
                "command": "take a photo with camera",
                "multimodal_type": "camera",
                "expected_timeout": 15,
                "description": "相机拍照命令"
            },
            {
                "command": "generate an image with AI",
                "multimodal_type": "ai_image_generator",
                "expected_timeout": 8,
                "description": "AI生图命令"
            }
        ]
        
        for test_case in multimodal_test_cases:
            with allure.step(f"测试{test_case['description']}的超时时间"):
                # 获取动态超时时间
                actual_timeout = self._get_response_timeout(
                    test_case["command"], 
                    test_case["multimodal_type"]
                )
                
                # 验证超时时间是否符合预期
                assert actual_timeout == test_case["expected_timeout"], \
                    f"{test_case['description']}的超时时间应该是{test_case['expected_timeout']}秒，实际是{actual_timeout}秒"
                
                log.info(f"✅ {test_case['description']}超时时间验证通过: {actual_timeout}秒")

    @allure.feature("Ella动态等待时间")
    @allure.story("命令类型超时测试")
    @allure.title("测试不同命令类型的动态超时时间")
    def test_command_type_timeout(self, ella_app):
        """
        测试不同命令类型的动态超时时间设置
        
        Args:
            ella_app: Ella应用实例
        """
        # 测试不同命令类型的超时时间
        command_test_cases = [
            {
                "command": "take a photo",
                "expected_timeout": 15,
                "category": "拍照命令"
            },
            {
                "command": "take a selfie",
                "expected_timeout": 15,
                "category": "自拍命令"
            },
            {
                "command": "select file from storage",
                "expected_timeout": 12,
                "category": "文件操作命令"
            },
            {
                "command": "open bluetooth settings",
                "expected_timeout": 10,
                "category": "应用启动命令"
            },
            {
                "command": "turn on bluetooth",
                "expected_timeout": 10,
                "category": "系统设置命令"
            },
            {
                "command": "hello how are you",
                "expected_timeout": 6,
                "category": "简单对话命令"
            },
            {
                "command": "what time is it now",
                "expected_timeout": 6,
                "category": "简单查询命令"
            },
            {
                "command": "unknown complex command",
                "expected_timeout": 8,
                "category": "默认命令"
            }
        ]
        
        for test_case in command_test_cases:
            with allure.step(f"测试{test_case['category']}: {test_case['command']}"):
                # 获取动态超时时间（不指定多模态类型）
                actual_timeout = self._get_response_timeout(test_case["command"])
                
                # 验证超时时间是否符合预期
                assert actual_timeout == test_case["expected_timeout"], \
                    f"{test_case['category']}的超时时间应该是{test_case['expected_timeout']}秒，实际是{actual_timeout}秒"
                
                log.info(f"✅ {test_case['category']}超时时间验证通过: {actual_timeout}秒")

    @allure.feature("Ella动态等待时间")
    @allure.story("优先级测试")
    @allure.title("测试多模态类型优先级高于命令内容")
    def test_multimodal_priority_over_command(self, ella_app):
        """
        测试多模态类型的优先级高于命令内容分析
        
        Args:
            ella_app: Ella应用实例
        """
        # 测试多模态类型优先级
        priority_test_cases = [
            {
                "command": "hello take a photo",  # 包含拍照关键词，但指定了document类型
                "multimodal_type": "document",
                "expected_timeout": 12,  # 应该使用document的超时时间，而不是拍照的15秒
                "description": "多模态类型优先于命令内容"
            },
            {
                "command": "open file selector",  # 包含文件关键词，但指定了camera类型
                "multimodal_type": "camera",
                "expected_timeout": 15,  # 应该使用camera的超时时间，而不是文件的12秒
                "description": "相机类型优先于文件关键词"
            }
        ]
        
        for test_case in priority_test_cases:
            with allure.step(f"测试{test_case['description']}"):
                # 获取动态超时时间
                actual_timeout = self._get_response_timeout(
                    test_case["command"], 
                    test_case["multimodal_type"]
                )
                
                # 验证多模态类型优先级
                assert actual_timeout == test_case["expected_timeout"], \
                    f"多模态类型应该优先，超时时间应该是{test_case['expected_timeout']}秒，实际是{actual_timeout}秒"
                
                log.info(f"✅ 优先级测试通过: {test_case['description']} - {actual_timeout}秒")

    @allure.feature("Ella动态等待时间")
    @allure.story("实际执行测试")
    @allure.title("测试动态超时时间在实际执行中的应用")
    def test_dynamic_timeout_in_execution(self, ella_app):
        """
        测试动态超时时间在实际命令执行中的应用
        
        Args:
            ella_app: Ella应用实例
        """
        # 测试不同超时时间的命令执行
        execution_test_cases = [
            {
                "command": "hello",
                "expected_category": "简单对话",
                "verify_status": False
            },
            {
                "command": "open bluetooth",
                "expected_category": "系统设置",
                "verify_status": True
            },
            {
                "command": "what time is it",
                "expected_category": "简单查询",
                "verify_status": False
            }
        ]
        
        for test_case in execution_test_cases:
            with allure.step(f"执行{test_case['expected_category']}命令: {test_case['command']}"):
                # 执行命令并验证
                initial_status, final_status, response_text, files_status = self.simple_command_test(
                    ella_app,
                    test_case["command"],
                    verify_status=test_case["verify_status"],
                    verify_files=False
                )
                
                # 验证执行成功
                assert isinstance(response_text, (str, list)), "应该获取到响应文本"
                log.info(f"✅ {test_case['expected_category']}命令执行成功")

    @allure.feature("Ella动态等待时间")
    @allure.story("边界条件测试")
    @allure.title("测试边界条件和异常情况")
    def test_timeout_edge_cases(self, ella_app):
        """
        测试超时时间的边界条件和异常情况
        
        Args:
            ella_app: Ella应用实例
        """
        # 测试边界条件
        edge_test_cases = [
            {
                "command": "",  # 空命令
                "multimodal_type": None,
                "expected_timeout": 8,
                "description": "空命令"
            },
            {
                "command": "   ",  # 空白命令
                "multimodal_type": None,
                "expected_timeout": 8,
                "description": "空白命令"
            },
            {
                "command": "normal command",
                "multimodal_type": "invalid_type",  # 无效的多模态类型
                "expected_timeout": 8,
                "description": "无效多模态类型"
            },
            {
                "command": "TAKE PHOTO",  # 大写命令
                "multimodal_type": None,
                "expected_timeout": 15,
                "description": "大写命令"
            },
            {
                "command": "拍照 take photo 照相",  # 混合语言
                "multimodal_type": None,
                "expected_timeout": 15,
                "description": "混合语言命令"
            }
        ]
        
        for test_case in edge_test_cases:
            with allure.step(f"测试{test_case['description']}"):
                # 获取动态超时时间
                actual_timeout = self._get_response_timeout(
                    test_case["command"], 
                    test_case["multimodal_type"]
                )
                
                # 验证边界条件处理
                assert actual_timeout == test_case["expected_timeout"], \
                    f"{test_case['description']}的超时时间应该是{test_case['expected_timeout']}秒，实际是{actual_timeout}秒"
                
                log.info(f"✅ {test_case['description']}边界条件测试通过: {actual_timeout}秒")

    @allure.feature("Ella动态等待时间")
    @allure.story("性能测试")
    @allure.title("测试超时时间计算的性能")
    def test_timeout_calculation_performance(self, ella_app):
        """
        测试超时时间计算的性能
        
        Args:
            ella_app: Ella应用实例
        """
        import time
        
        # 测试大量命令的超时时间计算性能
        test_commands = [
            "hello", "take photo", "open bluetooth", "select file",
            "what time", "launch camera", "turn on wifi", "choose image"
        ] * 100  # 800个命令
        
        with allure.step("测试超时时间计算性能"):
            start_time = time.time()
            
            for command in test_commands:
                timeout = self._get_response_timeout(command)
                assert isinstance(timeout, int), "超时时间应该是整数"
                assert timeout > 0, "超时时间应该大于0"
            
            end_time = time.time()
            calculation_time = end_time - start_time
            
            # 验证性能（800个命令应该在1秒内完成）
            assert calculation_time < 1.0, f"超时时间计算性能不佳，800个命令耗时{calculation_time:.3f}秒"
            
            log.info(f"✅ 超时时间计算性能测试通过: 800个命令耗时{calculation_time:.3f}秒")


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, "-v", "--allure-dir=reports/allure-results"])
