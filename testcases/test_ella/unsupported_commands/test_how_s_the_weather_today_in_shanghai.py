"""
Ella语音助手不支持的指令
"""
import re

import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("不支持的指令")
class TestEllaHowSWeatherTodayShanghai(SimpleEllaTest):
    """Ella how's the weather today in shanghai 测试类 - 验证不支持的指令响应"""
    command = "how's the weather today in shanghai"
    expected_text = ['shanghai','℃']

    @allure.title(f"测试{command}返回正确的不支持响应")
    @allure.description(f"验证{command}指令返回预期的不支持响应")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.smoke
    def test_how_s_the_weather_today_in_shanghai(self, ella_app):
        f"""{self.command} - 验证不支持指令的响应"""

        command = self.command

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command, verify_status=False  # 第三方集成通常不验证状态
            )


        with allure.step("验证响应包含期望的不支持内容"):
            expected_text = self.expected_text
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        # 从response_text列表中聚合字符串并提取温度数据，验证在合理范围内
        response_str = ' '.join(response_text) if isinstance(response_text, list) else str(response_text)
        temperature_pattern = r'(\d+)℃'
        temperatures = re.findall(temperature_pattern, response_str)

        with allure.step("验证提取到的温度数据"):
            if temperatures:
                # 转换为整数并验证温度范围合理性（-50℃ 到 60℃）
                temp_values = [int(temp) for temp in temperatures]
                for temp in temp_values:
                    assert -50 <= temp <= 60, f"温度值 {temp}℃ 超出合理范围 (-50℃ 到 60℃)"
                allure.attach(f"提取到的温度: {temp_values}℃", "温度数据", allure.attachment_type.TEXT)

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
