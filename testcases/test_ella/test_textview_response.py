"""
Ella TextView响应文本获取测试用例
测试新增的TextView元素文本获取功能
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


class TestEllaTextViewResponse(SimpleEllaTest):
    """Ella TextView响应文本获取测试类"""

    @allure.feature("Ella TextView响应")
    @allure.story("TextView元素文本获取")
    @allure.title("测试获取所有TextView元素文本")
    def test_get_all_textview_elements(self, ella_app):
        """
        测试获取所有TextView元素的文本
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("执行命令触发响应"):
            # 执行一个命令来确保页面有内容
            command = "hello"
            self.simple_command_test(ella_app, command, verify_status=False)
        
        with allure.step("获取所有TextView元素文本"):
            # 获取所有TextView元素文本
            textview_texts = ella_app.get_response_from_textview_elements()
            
            # 记录获取结果
            if textview_texts:
                allure.attach(
                    "\n".join(textview_texts), 
                    name="所有TextView元素文本", 
                    attachment_type=allure.attachment_type.TEXT
                )
                log.info(f"✅ 成功获取{len(textview_texts)}个TextView元素文本")
                for i, text in enumerate(textview_texts[:10]):  # 只显示前10个
                    log.info(f"TextView[{i}]: {text}")
            else:
                log.info("ℹ️ 未获取到任何TextView元素文本")
        
        # 验证：返回值应该是列表类型
        assert isinstance(textview_texts, list), "返回值应该是列表类型"

    @allure.feature("Ella TextView响应")
    @allure.story("可见TextView元素文本获取")
    @allure.title("测试获取可见TextView元素文本")
    def test_get_visible_textview_elements(self, ella_app):
        """
        测试获取可见TextView元素的文本
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("执行命令触发响应"):
            # 执行一个命令来确保页面有内容
            command = "what time is it"
            self.simple_command_test(ella_app, command, verify_status=False)
        
        with allure.step("获取可见TextView元素文本"):
            # 获取可见TextView元素文本
            visible_textview_texts = ella_app.get_response_from_visible_textviews()
            
            # 记录获取结果
            if visible_textview_texts:
                allure.attach(
                    "\n".join(visible_textview_texts), 
                    name="可见TextView元素文本", 
                    attachment_type=allure.attachment_type.TEXT
                )
                log.info(f"✅ 成功获取{len(visible_textview_texts)}个可见TextView元素文本")
                for i, text in enumerate(visible_textview_texts[:5]):  # 只显示前5个
                    log.info(f"可见TextView[{i}]: {text}")
            else:
                log.info("ℹ️ 未获取到任何可见TextView元素文本")
        
        # 验证：返回值应该是列表类型
        assert isinstance(visible_textview_texts, list), "返回值应该是列表类型"

    @allure.feature("Ella TextView响应")
    @allure.story("TextView文本模式匹配")
    @allure.title("测试根据文本模式匹配TextView元素")
    def test_get_textview_by_text_pattern(self, ella_app):
        """
        测试根据文本模式匹配获取TextView元素文本
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("执行命令触发响应"):
            # 执行一个包含特定关键词的命令
            command = "open bluetooth settings"
            self.simple_command_test(ella_app, command, verify_status=True)
        
        with allure.step("测试不同文本模式匹配"):
            # 测试不同的文本模式
            test_patterns = [
                "蓝牙",
                "bluetooth", 
                "设置",
                "settings",
                "已",
                "开启"
            ]
            
            matched_results = {}
            
            for pattern in test_patterns:
                matched_text = ella_app.get_response_from_textview_by_text(pattern)
                matched_results[pattern] = matched_text
                
                if matched_text:
                    log.info(f"✅ 模式'{pattern}'匹配到: {matched_text}")
                else:
                    log.info(f"ℹ️ 模式'{pattern}'未匹配到任何文本")
            
            # 记录匹配结果
            results_summary = "\n".join([
                f"模式'{pattern}': {text if text else '未匹配'}"
                for pattern, text in matched_results.items()
            ])
            allure.attach(
                results_summary,
                name="文本模式匹配结果",
                attachment_type=allure.attachment_type.TEXT
            )
        
        # 验证：至少应该有一个模式匹配成功
        successful_matches = [text for text in matched_results.values() if text]
        if successful_matches:
            log.info(f"✅ 成功匹配{len(successful_matches)}个文本模式")
        else:
            log.info("ℹ️ 未匹配到任何文本模式")

    @allure.feature("Ella TextView响应")
    @allure.story("TextView索引获取")
    @allure.title("测试根据索引获取TextView元素文本")
    def test_get_textview_by_index(self, ella_app):
        """
        测试根据索引获取TextView元素文本
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("执行命令触发响应"):
            # 执行一个命令来确保页面有内容
            command = "turn on wifi"
            self.simple_command_test(ella_app, command, verify_status=True)
        
        with allure.step("测试不同索引的TextView获取"):
            # 测试获取前几个TextView元素
            index_results = {}
            
            for index in range(5):  # 测试前5个索引
                textview_text = ella_app.get_response_from_textview_by_index(index)
                index_results[index] = textview_text
                
                if textview_text:
                    log.info(f"✅ 索引{index}的TextView文本: {textview_text}")
                else:
                    log.info(f"ℹ️ 索引{index}的TextView文本为空或不存在")
            
            # 记录索引获取结果
            results_summary = "\n".join([
                f"索引{index}: {text if text else '空或不存在'}"
                for index, text in index_results.items()
            ])
            allure.attach(
                results_summary,
                name="索引获取结果",
                attachment_type=allure.attachment_type.TEXT
            )
        
        # 验证：至少第一个索引应该有内容（通常情况下）
        first_textview = index_results.get(0, "")
        if first_textview:
            log.info(f"✅ 第一个TextView元素有内容: {first_textview}")

    @allure.feature("Ella TextView响应")
    @allure.story("TextView集成测试")
    @allure.title("测试TextView文本在综合响应中的应用")
    def test_textview_in_comprehensive_response(self, ella_app):
        """
        测试TextView文本在综合响应获取中的应用
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("执行命令触发响应"):
            # 执行一个命令
            command = "check battery status"
            self.simple_command_test(ella_app, command, verify_status=False)
        
        with allure.step("获取综合响应信息"):
            # 获取所有类型的响应文本
            all_response_texts = ella_app.get_response_all_text()  # 包含TextView文本
            textview_texts = ella_app.get_response_from_textview_elements()
            visible_textview_texts = ella_app.get_response_from_visible_textviews()
            
            # 记录综合响应信息
            comprehensive_summary = f"""
所有响应文本数量: {len(all_response_texts)}
TextView文本数量: {len(textview_texts)}
可见TextView文本数量: {len(visible_textview_texts)}

所有响应文本: {all_response_texts}
TextView文本: {textview_texts}
可见TextView文本: {visible_textview_texts}
"""
            allure.attach(
                comprehensive_summary,
                name="综合响应信息",
                attachment_type=allure.attachment_type.TEXT
            )
            
            log.info(f"所有响应文本数量: {len(all_response_texts)}")
            log.info(f"TextView文本数量: {len(textview_texts)}")
            log.info(f"可见TextView文本数量: {len(visible_textview_texts)}")
        
        with allure.step("验证TextView文本集成"):
            # 验证TextView文本是否已包含在综合响应中
            if textview_texts:
                # 检查是否有TextView文本包含在all_response_texts中
                textview_in_all = any(
                    any(tv_text in str(resp_text) for resp_text in all_response_texts if resp_text)
                    for tv_text in textview_texts if tv_text
                )
                
                if textview_in_all:
                    log.info("✅ TextView文本已正确集成到综合响应中")
                else:
                    log.info("ℹ️ TextView文本可能未完全集成到综合响应中")

    @allure.feature("Ella TextView响应")
    @allure.story("TextView错误处理")
    @allure.title("测试TextView获取的错误处理")
    def test_textview_error_handling(self, ella_app):
        """
        测试TextView文本获取的错误处理能力
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("测试无效索引处理"):
            # 测试超大索引
            large_index_result = ella_app.get_response_from_textview_by_index(9999)
            assert isinstance(large_index_result, str), "超大索引应返回字符串"
            log.info(f"超大索引(9999)结果: '{large_index_result}'")
            
            # 测试负数索引
            negative_index_result = ella_app.get_response_from_textview_by_index(-1)
            assert isinstance(negative_index_result, str), "负数索引应返回字符串"
            log.info(f"负数索引(-1)结果: '{negative_index_result}'")
        
        with allure.step("测试无效文本模式处理"):
            # 测试不存在的文本模式
            nonexistent_pattern = "这是一个不存在的文本模式12345"
            pattern_result = ella_app.get_response_from_textview_by_text(nonexistent_pattern)
            assert isinstance(pattern_result, str), "不存在的模式应返回字符串"
            log.info(f"不存在模式结果: '{pattern_result}'")
            
            # 测试空文本模式
            empty_pattern_result = ella_app.get_response_from_textview_by_text("")
            assert isinstance(empty_pattern_result, str), "空模式应返回字符串"
            log.info(f"空模式结果: '{empty_pattern_result}'")
        
        with allure.step("验证错误处理"):
            # 验证所有错误情况都能正常处理，不抛出异常
            log.info("✅ 所有错误处理测试通过，未抛出异常")

    @allure.feature("Ella TextView响应")
    @allure.story("TextView性能测试")
    @allure.title("测试TextView获取的性能")
    def test_textview_performance(self, ella_app):
        """
        测试TextView文本获取的性能
        
        Args:
            ella_app: Ella应用实例
        """
        import time
        
        with allure.step("执行命令准备测试环境"):
            command = "hello world"
            self.simple_command_test(ella_app, command, verify_status=False)
        
        with allure.step("测试TextView获取性能"):
            # 测试多次获取的性能
            iterations = 10
            
            # 测试获取所有TextView的性能
            start_time = time.time()
            for _ in range(iterations):
                textview_texts = ella_app.get_response_from_textview_elements()
            all_textview_time = time.time() - start_time
            
            # 测试获取可见TextView的性能
            start_time = time.time()
            for _ in range(iterations):
                visible_texts = ella_app.get_response_from_visible_textviews()
            visible_textview_time = time.time() - start_time
            
            # 测试索引获取的性能
            start_time = time.time()
            for _ in range(iterations):
                index_text = ella_app.get_response_from_textview_by_index(0)
            index_textview_time = time.time() - start_time
            
            # 记录性能结果
            performance_summary = f"""
{iterations}次获取所有TextView耗时: {all_textview_time:.3f}秒 (平均: {all_textview_time/iterations:.3f}秒)
{iterations}次获取可见TextView耗时: {visible_textview_time:.3f}秒 (平均: {visible_textview_time/iterations:.3f}秒)
{iterations}次索引获取耗时: {index_textview_time:.3f}秒 (平均: {index_textview_time/iterations:.3f}秒)
"""
            allure.attach(
                performance_summary,
                name="TextView性能测试结果",
                attachment_type=allure.attachment_type.TEXT
            )
            
            log.info(f"✅ TextView性能测试完成")
            log.info(f"获取所有TextView平均耗时: {all_textview_time/iterations:.3f}秒")
            log.info(f"获取可见TextView平均耗时: {visible_textview_time/iterations:.3f}秒")
            log.info(f"索引获取平均耗时: {index_textview_time/iterations:.3f}秒")
        
        # 验证性能（每次操作应该在合理时间内完成）
        assert all_textview_time/iterations < 5.0, "获取所有TextView的平均时间应该小于5秒"
        assert visible_textview_time/iterations < 3.0, "获取可见TextView的平均时间应该小于3秒"
        assert index_textview_time/iterations < 1.0, "索引获取的平均时间应该小于1秒"


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, "-v", "--allure-dir=reports/allure-results"])
