"""
Ella TextView修复验证测试用例
验证修复后的TextView文本获取功能是否正常工作
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


class TestEllaTextViewFixValidation(SimpleEllaTest):
    """Ella TextView修复验证测试类"""

    @allure.feature("TextView修复验证")
    @allure.story("基本功能验证")
    @allure.title("验证TextView基本获取功能")
    def test_textview_basic_functionality(self, ella_app):
        """
        验证TextView基本获取功能是否正常
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("执行命令触发响应"):
            command = "hello"
            self.simple_command_test(ella_app, command, verify_status=False)
        
        with allure.step("测试所有TextView获取方法"):
            # 测试获取所有TextView
            try:
                all_textviews = ella_app.get_response_from_textview_elements()
                assert isinstance(all_textviews, list), "应该返回列表类型"
                log.info(f"✅ 获取所有TextView成功，数量: {len(all_textviews)}")
                
                if all_textviews:
                    for i, text in enumerate(all_textviews[:5]):  # 显示前5个
                        log.info(f"TextView[{i}]: {text}")
                
            except Exception as e:
                log.error(f"❌ 获取所有TextView失败: {e}")
                pytest.fail(f"获取所有TextView失败: {e}")
            
            # 测试获取可见TextView
            try:
                visible_textviews = ella_app.get_response_from_visible_textviews()
                assert isinstance(visible_textviews, list), "应该返回列表类型"
                log.info(f"✅ 获取可见TextView成功，数量: {len(visible_textviews)}")
                
            except Exception as e:
                log.error(f"❌ 获取可见TextView失败: {e}")
                pytest.fail(f"获取可见TextView失败: {e}")
            
            # 测试索引获取
            try:
                first_textview = ella_app.get_response_from_textview_by_index(0)
                assert isinstance(first_textview, str), "应该返回字符串类型"
                log.info(f"✅ 索引获取成功，第一个TextView: '{first_textview}'")
                
            except Exception as e:
                log.error(f"❌ 索引获取失败: {e}")
                pytest.fail(f"索引获取失败: {e}")

    @allure.feature("TextView修复验证")
    @allure.story("错误处理验证")
    @allure.title("验证错误处理是否健壮")
    def test_textview_error_handling(self, ella_app):
        """
        验证TextView错误处理是否健壮
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("测试边界条件"):
            # 测试超大索引
            try:
                large_index_result = ella_app.get_response_from_textview_by_index(9999)
                assert isinstance(large_index_result, str), "超大索引应返回字符串"
                log.info(f"✅ 超大索引处理正常: '{large_index_result}'")
            except Exception as e:
                log.error(f"❌ 超大索引处理失败: {e}")
                pytest.fail(f"超大索引处理失败: {e}")
            
            # 测试负数索引
            try:
                negative_index_result = ella_app.get_response_from_textview_by_index(-1)
                assert isinstance(negative_index_result, str), "负数索引应返回字符串"
                log.info(f"✅ 负数索引处理正常: '{negative_index_result}'")
            except Exception as e:
                log.error(f"❌ 负数索引处理失败: {e}")
                pytest.fail(f"负数索引处理失败: {e}")
            
            # 测试空文本模式
            try:
                empty_pattern_result = ella_app.get_response_from_textview_by_text("")
                assert isinstance(empty_pattern_result, str), "空模式应返回字符串"
                log.info(f"✅ 空模式处理正常: '{empty_pattern_result}'")
            except Exception as e:
                log.error(f"❌ 空模式处理失败: {e}")
                pytest.fail(f"空模式处理失败: {e}")

    @allure.feature("TextView修复验证")
    @allure.story("性能验证")
    @allure.title("验证性能是否可接受")
    def test_textview_performance(self, ella_app):
        """
        验证TextView获取性能是否可接受
        
        Args:
            ella_app: Ella应用实例
        """
        import time
        
        with allure.step("执行命令准备环境"):
            command = "what time is it"
            self.simple_command_test(ella_app, command, verify_status=False)
        
        with allure.step("测试性能"):
            # 测试获取所有TextView的性能
            start_time = time.time()
            try:
                textviews = ella_app.get_response_from_textview_elements()
                end_time = time.time()
                duration = end_time - start_time
                
                log.info(f"✅ 获取所有TextView耗时: {duration:.3f}秒")
                log.info(f"获取到{len(textviews)}个TextView元素")
                
                # 验证性能（应该在合理时间内完成）
                assert duration < 10.0, f"获取TextView耗时过长: {duration:.3f}秒"
                
            except Exception as e:
                log.error(f"❌ 性能测试失败: {e}")
                pytest.fail(f"性能测试失败: {e}")

    @allure.feature("TextView修复验证")
    @allure.story("集成验证")
    @allure.title("验证与现有系统的集成")
    def test_textview_integration(self, ella_app):
        """
        验证TextView功能与现有系统的集成
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("执行命令触发响应"):
            command = "open bluetooth"
            self.simple_command_test(ella_app, command, verify_status=True)
        
        with allure.step("验证集成功能"):
            # 验证TextView文本是否包含在综合响应中
            try:
                all_response_texts = ella_app.get_response_all_text()
                textview_texts = ella_app.get_response_from_textview_elements()
                
                log.info(f"综合响应数量: {len(all_response_texts)}")
                log.info(f"TextView文本数量: {len(textview_texts)}")
                
                # 验证类型
                assert isinstance(all_response_texts, list), "综合响应应该是列表"
                assert isinstance(textview_texts, list), "TextView文本应该是列表"
                
                # 验证TextView文本是否已包含在综合响应中
                if textview_texts:
                    # 检查是否有TextView文本包含在all_response_texts中
                    found_integration = False
                    for tv_text in textview_texts:
                        if tv_text and any(tv_text in str(resp) for resp in all_response_texts if resp):
                            found_integration = True
                            break
                    
                    if found_integration:
                        log.info("✅ TextView文本已正确集成到综合响应中")
                    else:
                        log.info("ℹ️ TextView文本可能作为独立项目添加到综合响应中")
                
                log.info("✅ 集成验证完成")
                
            except Exception as e:
                log.error(f"❌ 集成验证失败: {e}")
                pytest.fail(f"集成验证失败: {e}")

    @allure.feature("TextView修复验证")
    @allure.story("文本匹配验证")
    @allure.title("验证文本模式匹配功能")
    def test_textview_text_matching(self, ella_app):
        """
        验证文本模式匹配功能
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("执行命令触发响应"):
            command = "turn on wifi"
            self.simple_command_test(ella_app, command, verify_status=True)
        
        with allure.step("测试文本匹配"):
            # 获取所有TextView文本作为参考
            try:
                all_textviews = ella_app.get_response_from_textview_elements()
                log.info(f"可用于匹配的TextView文本: {all_textviews}")
                
                if all_textviews:
                    # 使用第一个非空文本的一部分进行匹配测试
                    for text in all_textviews:
                        if text and len(text) > 2:
                            # 取文本的前几个字符进行匹配测试
                            pattern = text[:3] if len(text) >= 3 else text
                            
                            try:
                                matched_text = ella_app.get_response_from_textview_by_text(pattern)
                                if matched_text:
                                    log.info(f"✅ 成功匹配模式'{pattern}': '{matched_text}'")
                                    break
                                else:
                                    log.info(f"ℹ️ 模式'{pattern}'未匹配到文本")
                            except Exception as match_e:
                                log.error(f"❌ 匹配模式'{pattern}'失败: {match_e}")
                                continue
                
                log.info("✅ 文本匹配验证完成")
                
            except Exception as e:
                log.error(f"❌ 文本匹配验证失败: {e}")
                pytest.fail(f"文本匹配验证失败: {e}")

    @allure.feature("TextView修复验证")
    @allure.story("备用方法验证")
    @allure.title("验证备用获取方法")
    def test_textview_backup_methods(self, ella_app):
        """
        验证备用获取方法是否正常工作
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("执行命令准备环境"):
            command = "hello world"
            self.simple_command_test(ella_app, command, verify_status=False)
        
        with allure.step("测试备用方法"):
            # 直接测试安全获取方法
            try:
                # 通过response_handler直接调用安全方法
                safe_texts = ella_app.response_handler._get_textview_elements_safe(20)
                
                assert isinstance(safe_texts, list), "安全方法应该返回列表"
                log.info(f"✅ 安全获取方法成功，获取{len(safe_texts)}个文本")
                
                if safe_texts:
                    for i, text in enumerate(safe_texts[:3]):  # 显示前3个
                        log.info(f"安全方法TextView[{i}]: {text}")
                
            except Exception as e:
                log.error(f"❌ 安全获取方法失败: {e}")
                # 不让测试失败，因为这是备用方法
                log.warning("备用方法失败，但这不影响主要功能")

    @allure.feature("TextView修复验证")
    @allure.story("稳定性验证")
    @allure.title("验证多次调用的稳定性")
    def test_textview_stability(self, ella_app):
        """
        验证多次调用TextView获取方法的稳定性
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("执行命令准备环境"):
            command = "check battery"
            self.simple_command_test(ella_app, command, verify_status=False)
        
        with allure.step("测试多次调用稳定性"):
            success_count = 0
            total_attempts = 5
            
            for i in range(total_attempts):
                try:
                    textviews = ella_app.get_response_from_textview_elements()
                    if isinstance(textviews, list):
                        success_count += 1
                        log.info(f"第{i+1}次调用成功，获取{len(textviews)}个文本")
                    else:
                        log.warning(f"第{i+1}次调用返回类型异常: {type(textviews)}")
                        
                except Exception as e:
                    log.error(f"第{i+1}次调用失败: {e}")
                
                # 短暂等待
                import time
                time.sleep(0.5)
            
            success_rate = success_count / total_attempts
            log.info(f"✅ 稳定性测试完成，成功率: {success_rate:.1%} ({success_count}/{total_attempts})")
            
            # 验证稳定性（至少80%成功率）
            assert success_rate >= 0.8, f"稳定性不足，成功率仅{success_rate:.1%}"


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, "-v", "--allure-dir=reports/allure-results"])
