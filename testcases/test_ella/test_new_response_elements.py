"""
Ella新增响应元素测试用例
测试新增的tv_card_chat_gpt和tv_top元素文案获取功能
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


class TestEllaNewResponseElements(SimpleEllaTest):
    """Ella新增响应元素测试类"""

    @allure.feature("Ella响应处理")
    @allure.story("新增元素文案获取")
    @allure.title("测试tv_card_chat_gpt元素文案获取")
    def test_get_tv_card_chat_gpt_text(self, ella_app):
        """
        测试获取tv_card_chat_gpt元素的文案
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("执行命令触发响应"):
            # 执行一个命令来触发AI响应
            command = "hello"
            self.simple_command_test(ella_app, command, verify_status=False)
        
        with allure.step("获取tv_card_chat_gpt文案"):
            # 获取卡片聊天内容
            card_chat_text = ella_app.get_response_from_tv_card_chat_gpt()
            
            # 记录获取结果
            if card_chat_text:
                allure.attach(
                    card_chat_text, 
                    name="tv_card_chat_gpt文案", 
                    attachment_type=allure.attachment_type.TEXT
                )
                log.info(f"✅ 成功获取tv_card_chat_gpt文案: {card_chat_text}")
            else:
                log.info("ℹ️ tv_card_chat_gpt元素未找到或文案为空")
        
        # 验证：无论是否获取到内容，都不应该抛出异常
        assert isinstance(card_chat_text, str), "返回值应该是字符串类型"

    @allure.feature("Ella响应处理")
    @allure.story("新增元素文案获取")
    @allure.title("测试tv_top元素文案获取")
    def test_get_tv_top_text(self, ella_app):
        """
        测试获取tv_top元素的文案
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("执行命令触发响应"):
            # 执行一个命令来触发AI响应
            command = "what time is it"
            self.simple_command_test(ella_app, command, verify_status=False)
        
        with allure.step("获取tv_top文案"):
            # 获取顶部文本内容
            top_text = ella_app.get_response_from_tv_top()
            
            # 记录获取结果
            if top_text:
                allure.attach(
                    top_text, 
                    name="tv_top文案", 
                    attachment_type=allure.attachment_type.TEXT
                )
                log.info(f"✅ 成功获取tv_top文案: {top_text}")
            else:
                log.info("ℹ️ tv_top元素未找到或文案为空")
        
        # 验证：无论是否获取到内容，都不应该抛出异常
        assert isinstance(top_text, str), "返回值应该是字符串类型"

    @allure.feature("Ella响应处理")
    @allure.story("新增元素文案获取")
    @allure.title("测试所有响应元素文案获取")
    def test_get_all_response_elements(self, ella_app):
        """
        测试获取所有响应元素的文案，包括新增的两个元素
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("执行命令触发响应"):
            # 执行一个命令来触发AI响应
            command = "open bluetooth"
            self.simple_command_test(ella_app, command, verify_status=True)
        
        with allure.step("获取所有响应元素文案"):
            # 获取所有响应文本（包括新增的元素）
            all_response_texts = ella_app.get_response_all_text()
            
            # 单独获取新增的元素文案
            card_chat_text = ella_app.get_response_from_tv_card_chat_gpt()
            top_text = ella_app.get_response_from_tv_top()
            
            # 记录所有获取结果
            response_summary = f"""
所有响应文本: {all_response_texts}
卡片聊天内容: {card_chat_text}
顶部文本内容: {top_text}
"""
            allure.attach(
                response_summary, 
                name="所有响应元素文案", 
                attachment_type=allure.attachment_type.TEXT
            )
            
            log.info(f"所有响应文本: {all_response_texts}")
            log.info(f"卡片聊天内容: {card_chat_text}")
            log.info(f"顶部文本内容: {top_text}")
        
        with allure.step("验证响应内容"):
            # 验证基本类型
            assert isinstance(all_response_texts, list), "get_response_all_text应该返回列表"
            assert isinstance(card_chat_text, str), "get_response_from_tv_card_chat_gpt应该返回字符串"
            assert isinstance(top_text, str), "get_response_from_tv_top应该返回字符串"
            
            # 验证新增元素是否已包含在all_response_texts中
            if card_chat_text:
                assert card_chat_text in all_response_texts, "卡片聊天内容应该包含在所有响应文本中"
                log.info("✅ 卡片聊天内容已正确包含在所有响应文本中")
            
            if top_text:
                assert top_text in all_response_texts, "顶部文本内容应该包含在所有响应文本中"
                log.info("✅ 顶部文本内容已正确包含在所有响应文本中")

    @allure.feature("Ella响应处理")
    @allure.story("新增元素文案获取")
    @allure.title("测试多模态命令的响应元素")
    def test_multimodal_response_elements(self, ella_app):
        """
        测试多模态命令的响应元素文案获取
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("执行多模态命令"):
            # 执行一个多模态相关的命令
            command = "help me take a photo"
            self.simple_command_test(
                ella_app, 
                command, 
                verify_status=False,
                multimodal_type="camera"
            )
        
        with allure.step("获取多模态响应元素文案"):
            # 获取所有响应元素
            all_texts = ella_app.get_response_all_text()
            card_text = ella_app.get_response_from_tv_card_chat_gpt()
            top_text = ella_app.get_response_from_tv_top()
            
            # 记录结果
            multimodal_summary = f"""
多模态命令: {command}
所有响应: {all_texts}
卡片内容: {card_text}
顶部内容: {top_text}
"""
            allure.attach(
                multimodal_summary,
                name="多模态响应元素文案",
                attachment_type=allure.attachment_type.TEXT
            )
            
            log.info(f"多模态命令响应 - 所有文本: {all_texts}")
            log.info(f"多模态命令响应 - 卡片内容: {card_text}")
            log.info(f"多模态命令响应 - 顶部内容: {top_text}")
        
        with allure.step("验证多模态响应"):
            # 验证响应中是否包含相机相关内容
            combined_response = " ".join(str(text) for text in all_texts if text)
            if card_text:
                combined_response += " " + card_text
            if top_text:
                combined_response += " " + top_text
            
            camera_keywords = ["相机", "拍照", "camera", "photo", "take"]
            found_keywords = [kw for kw in camera_keywords if kw.lower() in combined_response.lower()]
            
            if found_keywords:
                log.info(f"✅ 在响应中找到相机相关关键词: {found_keywords}")
            else:
                log.info("ℹ️ 未在响应中找到明确的相机相关关键词")

    @allure.feature("Ella响应处理")
    @allure.story("新增元素文案获取")
    @allure.title("测试响应元素的错误处理")
    def test_response_elements_error_handling(self, ella_app):
        """
        测试响应元素获取的错误处理能力
        
        Args:
            ella_app: Ella应用实例
        """
        with allure.step("测试在无响应状态下获取元素文案"):
            # 不执行任何命令，直接尝试获取文案
            card_text = ella_app.get_response_from_tv_card_chat_gpt()
            top_text = ella_app.get_response_from_tv_top()
            
            # 记录结果
            log.info(f"无响应状态 - 卡片内容: '{card_text}'")
            log.info(f"无响应状态 - 顶部内容: '{top_text}'")
            
            # 验证错误处理
            assert isinstance(card_text, str), "即使无内容也应返回字符串"
            assert isinstance(top_text, str), "即使无内容也应返回字符串"
            
            # 通常情况下，无响应时应该返回空字符串
            log.info("✅ 错误处理正常，返回了字符串类型的结果")


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, "-v", "--allure-dir=reports/allure-results"])
