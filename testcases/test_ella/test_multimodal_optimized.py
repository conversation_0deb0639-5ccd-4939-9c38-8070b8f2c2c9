"""
Ella多模态功能优化测试用例
展示如何使用优化后的simple_command_test函数结合多模态功能
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


class TestEllaMultimodalOptimized(SimpleEllaTest):
    """Ella多模态功能优化测试类"""

    @allure.feature("Ella多模态功能")
    @allure.story("文档功能测试")
    @allure.title("测试文档选择功能（优化版）")
    def test_document_function_optimized(self, ella_app):
        """
        测试文档选择功能 - 使用优化的多模态支持
        
        Args:
            ella_app: Ella应用实例
        """
        command = "help me select a document"
        
        # 使用优化后的simple_command_test，指定多模态类型为document
        initial_status, final_status, response_text, files_status = self.simple_command_test(
            ella_app, 
            command, 
            verify_status=False,  # 文档功能不需要验证状态变化
            verify_files=False,   # 暂不验证文件
            multimodal_type="document"  # 指定多模态类型
        )
        
        # 验证响应内容
        expected_keywords = ["文档", "文件", "选择", "document", "file", "select"]
        self.verify_expected_in_response_advanced(
            expected_keywords, 
            response_text, 
            match_any=True  # 只要包含任意一个关键词即可
        )

    @allure.feature("Ella多模态功能")
    @allure.story("图库功能测试")
    @allure.title("测试图库选择功能（优化版）")
    def test_gallery_function_optimized(self, ella_app):
        """
        测试图库选择功能 - 使用优化的多模态支持
        
        Args:
            ella_app: Ella应用实例
        """
        command = "help me choose a photo from gallery"
        
        # 使用优化后的simple_command_test，指定多模态类型为gallery
        initial_status, final_status, response_text, files_status = self.simple_command_test(
            ella_app, 
            command, 
            verify_status=False,
            verify_files=False,
            multimodal_type="gallery"  # 指定多模态类型
        )
        
        # 验证响应内容
        expected_keywords = ["图库", "照片", "图片", "gallery", "photo", "image"]
        self.verify_expected_in_response_advanced(
            expected_keywords, 
            response_text, 
            match_any=True
        )

    @allure.feature("Ella多模态功能")
    @allure.story("相机功能测试")
    @allure.title("测试相机拍照功能（优化版）")
    def test_camera_function_optimized(self, ella_app):
        """
        测试相机拍照功能 - 使用优化的多模态支持
        
        Args:
            ella_app: Ella应用实例
        """
        command = "take a photo with camera"
        
        # 使用优化后的simple_command_test，指定多模态类型为camera
        initial_status, final_status, response_text, files_status = self.simple_command_test(
            ella_app, 
            command, 
            verify_status=False,
            verify_files=True,  # 相机功能需要验证文件生成
            multimodal_type="camera"  # 指定多模态类型
        )
        
        # 验证响应内容
        expected_keywords = ["相机", "拍照", "照片", "camera", "photo", "capture"]
        self.verify_expected_in_response_advanced(
            expected_keywords, 
            response_text, 
            match_any=True
        )

    @allure.feature("Ella多模态功能")
    @allure.story("AI生图功能测试")
    @allure.title("测试AI生图功能（优化版）")
    def test_ai_image_generator_optimized(self, ella_app):
        """
        测试AI生图功能 - 使用优化的多模态支持
        
        Args:
            ella_app: Ella应用实例
        """
        command = "generate an image with AI"
        
        # 使用优化后的simple_command_test，指定多模态类型为ai_image_generator
        initial_status, final_status, response_text, files_status = self.simple_command_test(
            ella_app, 
            command, 
            verify_status=False,
            verify_files=False,
            multimodal_type="ai_image_generator"  # 指定多模态类型
        )
        
        # 验证响应内容
        expected_keywords = ["AI", "生图", "生成", "图片", "generate", "image", "create"]
        self.verify_expected_in_response_advanced(
            expected_keywords, 
            response_text, 
            match_any=True
        )

    @allure.feature("Ella多模态功能")
    @allure.story("无效多模态类型测试")
    @allure.title("测试无效多模态类型处理")
    def test_invalid_multimodal_type(self, ella_app):
        """
        测试无效多模态类型的处理 - 应该跳过多模态处理
        
        Args:
            ella_app: Ella应用实例
        """
        command = "open bluetooth"
        
        # 使用无效的多模态类型，应该跳过多模态处理
        initial_status, final_status, response_text, files_status = self.simple_command_test(
            ella_app, 
            command, 
            verify_status=True,  # 蓝牙功能需要验证状态变化
            verify_files=False,
            multimodal_type="invalid_type"  # 无效的多模态类型
        )
        
        # 验证响应内容
        expected_keywords = ["蓝牙", "bluetooth", "开启", "open"]
        self.verify_expected_in_response_advanced(
            expected_keywords, 
            response_text, 
            match_any=True
        )

    @allure.feature("Ella多模态功能")
    @allure.story("无多模态类型测试")
    @allure.title("测试不指定多模态类型（传统模式）")
    def test_no_multimodal_type(self, ella_app):
        """
        测试不指定多模态类型 - 使用传统的测试模式
        
        Args:
            ella_app: Ella应用实例
        """
        command = "close bluetooth"
        
        # 不指定多模态类型，使用传统模式
        initial_status, final_status, response_text, files_status = self.simple_command_test(
            ella_app, 
            command, 
            verify_status=True,
            verify_files=False
            # 不指定multimodal_type参数，默认为None
        )
        
        # 验证响应内容
        expected_keywords = ["蓝牙", "bluetooth", "关闭", "close"]
        self.verify_expected_in_response_advanced(
            expected_keywords, 
            response_text, 
            match_any=True
        )

    @allure.feature("Ella多模态功能")
    @allure.story("混合测试")
    @allure.title("测试多模态功能混合使用")
    def test_multimodal_mixed_usage(self, ella_app):
        """
        测试多模态功能的混合使用场景
        
        Args:
            ella_app: Ella应用实例
        """
        # 测试序列：文档 -> 图库 -> 相机
        test_cases = [
            {
                "command": "select a document file",
                "multimodal_type": "document",
                "expected_keywords": ["文档", "文件", "document", "file"]
            },
            {
                "command": "choose a photo",
                "multimodal_type": "gallery", 
                "expected_keywords": ["图库", "照片", "gallery", "photo"]
            },
            {
                "command": "take a selfie",
                "multimodal_type": "camera",
                "expected_keywords": ["相机", "拍照", "camera", "selfie"]
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            with allure.step(f"执行第{i+1}个测试: {test_case['command']}"):
                initial_status, final_status, response_text, files_status = self.simple_command_test(
                    ella_app,
                    test_case["command"],
                    verify_status=False,
                    verify_files=(test_case["multimodal_type"] == "camera"),  # 只有相机需要验证文件
                    multimodal_type=test_case["multimodal_type"]
                )
                
                # 验证响应内容
                self.verify_expected_in_response_advanced(
                    test_case["expected_keywords"],
                    response_text,
                    match_any=True
                )


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, "-v", "--allure-dir=reports/allure-results"])
