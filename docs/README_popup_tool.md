# 弹窗处理工具 (PopupTool)

## 概述

`popup_tool.py` 是一个基于 UIAutomator2 的简化弹窗处理工具，参考 `tools/pop_example.py` 实现，提供简洁易用的API来检测和处理Android应用中的各种弹窗。

## 主要特性

- ✅ **多种检测方法**: UI层次结构、位置检测、关键词匹配
- ✅ **智能关闭策略**: 预定义常见关闭按钮文本和资源ID
- ✅ **操作包装器**: 为常见操作提供自动弹窗处理
- ✅ **简洁API**: 易于使用，减少配置复杂度
- ✅ **日志集成**: 与项目日志系统无缝集成
- ✅ **全面测试**: 31个测试用例，100%通过

## 快速开始

### 基本使用

```python
from core.popup_tool import create_popup_tool

# 创建弹窗处理工具
popup_tool = create_popup_tool()

# 检测并关闭弹窗
if popup_tool.detect_and_close_popup(timeout=10):
    print("成功处理弹窗")
```

### 自动化操作

```python
from core.popup_tool import create_automation_with_popup

# 创建自动化操作工具
automation = create_automation_with_popup()

# 执行操作，自动处理弹窗
automation.click(500, 300)
automation.input_text("测试文本")
automation.click_element({'resourceId': 'com.example:id/button'})
```

## 核心类

### PopupTool

主要的弹窗处理工具类，提供以下功能：

- `detect_and_close_popup()` - 检测并关闭弹窗
- `is_popup_present()` - 检测弹窗是否存在
- `safe_action()` - 为任意操作添加弹窗处理
- `click_with_popup_handling()` - 带弹窗处理的点击
- `input_with_popup_handling()` - 带弹窗处理的输入
- `element_click_with_popup_handling()` - 带弹窗处理的元素点击

### AutomationWithPopupTool

简化的自动化操作类，所有操作都自动处理弹窗：

- `click(x, y)` - 点击坐标
- `input_text(text)` - 输入文本
- `click_element(selector)` - 点击元素
- `detect_and_close_popup()` - 检测并关闭弹窗
- `is_popup_present()` - 检测弹窗存在

## 预定义配置

### 关闭按钮文本
```python
['关闭', '取消', '确定', '同意', '知道了', '我知道了',
 '立即体验', '稍后再说', '跳过', '继续', '允许', '拒绝',
 'X', '×', '✕', 'Close', 'OK', 'Cancel', 'Skip', ...]
```

### 资源ID
```python
['android:id/button1', 'android:id/button2',
 'com.android.packageinstaller:id/permission_allow_button', ...]
```

### 弹窗类型
```python
['android.app.Dialog', 'android.app.AlertDialog',
 'android.widget.PopupWindow', ...]
```

## 文件结构

```
core/
├── popup_tool.py              # 主要实现文件
├── README_popup_tool.md       # 本文档
└── __init__.py               # 更新了导入

tests/
└── test_popup_tool.py        # 测试文件 (31个测试用例)

docs/
└── popup_tool_usage.md      # 详细使用指南

examples/
└── popup_tool_demo.py       # 演示脚本
```

## 与现有系统的关系

这个工具是对现有弹窗处理系统的补充：

- **现有系统** (`core/popup_handler.py`, `core/popup_monitor.py`): 功能完整，配置丰富
- **新工具** (`core/popup_tool.py`): 简化版本，易于使用，快速上手

可以根据需求选择使用：
- 简单场景 → 使用 `popup_tool.py`
- 复杂场景 → 使用现有的完整系统
- 混合使用 → 两者结合

## 测试结果

```
31 passed in 6.16s
```

所有测试用例都通过，包括：
- 初始化测试
- 配置内容测试
- 弹窗检测测试
- 操作包装测试
- 便捷函数测试

## 使用示例

查看以下文件获取更多示例：
- `docs/popup_tool_usage.md` - 详细使用指南
- `examples/popup_tool_demo.py` - 演示脚本

## 注意事项

1. 工具基于 UIAutomator2，需要确保设备连接正常
2. 超时时间根据应用响应速度合理设置
3. 关注日志输出了解弹窗处理情况
4. 在关键流程中添加异常处理

## 导入方式

```python
# 直接导入
from core.popup_tool import PopupTool, AutomationWithPopupTool

# 使用便捷函数
from core.popup_tool import create_popup_tool, create_automation_with_popup

# 从core包导入
from core import PopupTool, create_popup_tool
```
