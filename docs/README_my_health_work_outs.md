# My Health Work Outs页面 (MyHealthWorkOutsPage) 使用指南

## 概述

`MyHealthWorkOutsPage` 是My Health应用Work Outs页面的页面对象类，参考Ella的`dialogue_page`实现模式，提供了完整的锻炼页面交互功能。该类继承自 `CommonPage`，遵循项目的PO（Page Object）设计模式。

## 功能特性

### 🔍 核心功能
- **应用启动**: 支持多种方式启动My Health应用
- **页面导航**: 导航到Work Outs页面，以及返回主页
- **搜索功能**: 搜索锻炼内容，支持清空搜索
- **分类浏览**: 按分类查看锻炼（全部、有氧运动、力量训练、瑜伽等）
- **锻炼管理**: 获取锻炼列表、选择锻炼、开始锻炼
- **页面交互**: 滚动、刷新、状态检查等

### 🛠️ 高级功能
- **智能等待**: 等待页面和内容加载完成
- **错误处理**: 网络错误检测和处理
- **状态检查**: 空状态、加载状态检测
- **综合测试**: 一键执行完整功能测试
- **模块化设计**: 功能模块分离，易于维护和扩展

## 快速开始

### 基本使用

```python
from pages.apps.my_health.work_outs_page import MyHealthWorkOutsPage

# 初始化页面对象
work_outs_page = MyHealthWorkOutsPage()

# 启动应用
work_outs_page.start_app()
work_outs_page.wait_for_page_load()

# 导航到Work Outs页面
work_outs_page.navigate_to_work_outs()

# 搜索锻炼
work_outs_page.search_workout("yoga")

# 选择分类
work_outs_page.select_category("cardio")

# 获取锻炼列表
workouts = work_outs_page.get_workout_list()

# 开始锻炼
work_outs_page.start_workout(0)  # 开始第一个锻炼
```

### 综合测试

```python
# 执行完整的功能测试
result = work_outs_page.perform_comprehensive_test()
print(f"测试结果: {result}")
```

## 详细功能说明

### 1. 应用启动和页面管理

#### 启动应用
```python
# 启动My Health应用
success = work_outs_page.start_app()
```

#### 等待页面加载
```python
# 等待页面加载完成
loaded = work_outs_page.wait_for_page_load(timeout=15)
```

#### 页面导航
```python
# 导航到Work Outs页面
work_outs_page.navigate_to_work_outs()

# 返回主页
work_outs_page.navigate_to_home()
```

#### 停止应用
```python
# 停止应用
work_outs_page.stop_app()
```

### 2. 搜索功能

#### 搜索锻炼
```python
# 搜索特定锻炼
work_outs_page.search_workout("瑜伽")
work_outs_page.search_workout("cardio")
```

#### 清空搜索
```python
# 清空搜索内容
work_outs_page.clear_search()
```

### 3. 分类功能

#### 选择分类
```python
# 选择预定义分类
work_outs_page.select_category("all")      # 全部
work_outs_page.select_category("cardio")   # 有氧运动
work_outs_page.select_category("strength") # 力量训练
work_outs_page.select_category("yoga")     # 瑜伽

# 选择自定义分类
work_outs_page.select_category("自定义分类名")
```

#### 获取可用分类
```python
# 获取所有可用分类
categories = work_outs_page.get_available_categories()
print(f"可用分类: {categories}")
```

### 4. 锻炼列表操作

#### 获取锻炼信息
```python
# 获取锻炼数量
count = work_outs_page.get_workout_count()

# 获取锻炼列表
workouts = work_outs_page.get_workout_list()
for workout in workouts:
    print(f"锻炼: {workout['title']}")
```

#### 选择和开始锻炼
```python
# 选择特定锻炼
work_outs_page.select_workout(0)  # 选择第一个锻炼

# 开始锻炼
work_outs_page.start_workout(0)   # 开始第一个锻炼
```

### 5. 页面交互

#### 滚动操作
```python
# 滚动到顶部
work_outs_page.scroll_to_top()

# 滚动到底部
work_outs_page.scroll_to_bottom()
```

#### 刷新页面
```python
# 刷新页面内容
work_outs_page.refresh_page()
```

### 6. 状态检查

#### 页面状态
```python
# 确保在Work Outs页面
work_outs_page.ensure_on_work_outs_page()

# 检查应用是否打开
app_opened = work_outs_page.check_my_health_app_opened()
```

#### 内容状态
```python
# 检查空状态
is_empty = work_outs_page.check_empty_state()

# 检查加载状态
is_loading = work_outs_page.check_loading_state()
```

#### 错误处理
```python
# 处理网络错误
work_outs_page.handle_network_error()
```

## 设计模式

### 参考Ella的dialogue_page实现

本页面类参考了Ella的`dialogue_page`实现模式，具有以下特点：

1. **模块化设计**: 功能分离到不同的方法组
2. **元素封装**: 页面元素统一管理
3. **错误处理**: 完善的异常处理机制
4. **状态检查**: 智能的页面状态检测
5. **兼容性**: 支持中英文界面

### 页面元素定义

```python
def _init_elements(self):
    """初始化页面元素"""
    # 主要元素
    self.work_outs_title = self.create_element(
        {"text": "Work Outs"}, "Work Outs页面标题"
    )
    
    self.search_box = self.create_element(
        {"resourceId": "com.transsion.healthlife:id/search_edit_text"}, "搜索框"
    )
    
    # 更多元素...
```

### 功能模块初始化

```python
def _init_modules(self):
    """初始化功能模块"""
    self.status_checker = SystemStatusChecker(self.driver)
    self.app_detector = AppDetector()
    self.process_monitor = AdbProcessMonitor()
```

## 运行示例

### 使用示例脚本

```bash
# 运行示例脚本
python examples/my_health_work_outs_example.py
```

示例脚本提供三种运行模式：
1. **完整功能演示**: 展示所有主要功能
2. **综合测试**: 执行自动化测试
3. **特定功能测试**: 测试特定功能模块

### 集成到测试用例

```python
import pytest
from pages.apps.my_health.work_outs_page import MyHealthWorkOutsPage

class TestMyHealthWorkOuts:
    def setup_method(self):
        self.work_outs_page = MyHealthWorkOutsPage()
    
    def test_app_startup(self):
        assert self.work_outs_page.start_app()
        assert self.work_outs_page.wait_for_page_load()
    
    def test_search_functionality(self):
        self.work_outs_page.start_app()
        assert self.work_outs_page.search_workout("yoga")
        assert self.work_outs_page.clear_search()
    
    def test_category_selection(self):
        self.work_outs_page.start_app()
        categories = self.work_outs_page.get_available_categories()
        if categories:
            assert self.work_outs_page.select_category(categories[0])
```

## 故障排除

### 常见问题

1. **应用启动失败**
   - 检查设备上是否安装了My Health应用
   - 确认应用包名是否正确：`com.transsion.healthlife`
   - 检查UIAutomator2服务状态

2. **页面元素找不到**
   - 确认应用版本是否匹配
   - 检查元素定位器是否正确
   - 尝试使用备选定位器

3. **网络相关功能失败**
   - 检查设备网络连接
   - 确认应用有网络权限
   - 使用`handle_network_error()`方法处理

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查页面状态
work_outs_page._check_work_outs_page_indicators()

# 获取当前应用信息
current_app = work_outs_page.driver.app_current()
print(f"当前应用: {current_app}")
```

## 扩展开发

### 添加新功能

1. 在`_init_elements()`中添加新的页面元素
2. 创建对应的功能方法
3. 在`page_elements`字典中注册新元素
4. 更新综合测试方法

### 自定义配置

```python
# 继承并自定义
class CustomWorkOutsPage(MyHealthWorkOutsPage):
    def __init__(self):
        super().__init__()
        # 添加自定义配置
        self.custom_timeout = 30
    
    def custom_search(self, query):
        # 自定义搜索逻辑
        return super().search_workout(query)
```

## 总结

`MyHealthWorkOutsPage`类提供了完整的My Health应用Work Outs页面自动化测试功能，参考Ella的设计模式，具有良好的可维护性和扩展性。通过模块化设计和完善的错误处理，能够稳定地支持各种测试场景。
