# 弹窗处理工具使用指南

## 概述

`core/popup_tool.py` 是一个简化的弹窗处理工具，基于 UIAutomator2 实现，提供了简洁易用的 API 来检测和处理 Android 应用中的各种弹窗。

## 主要特性

- **多种检测方法**: 支持通过 UI 层次结构、位置、关键词等方式检测弹窗
- **智能关闭策略**: 自动识别常见的关闭按钮文本和资源 ID
- **操作包装器**: 为常见操作提供自动弹窗处理功能
- **简洁 API**: 提供易于使用的接口，减少配置复杂度
- **日志集成**: 与项目日志系统集成，便于调试和监控

## 快速开始

### 基本使用

```python
from core.popup_tool import create_popup_tool

# 创建弹窗处理工具
popup_tool = create_popup_tool()

# 检测并关闭弹窗
if popup_tool.detect_and_close_popup(timeout=10):
    print("成功处理弹窗")
else:
    print("未发现弹窗或处理失败")
```

### 自动化操作

```python
from core.popup_tool import create_automation_with_popup

# 创建自动化操作工具
automation = create_automation_with_popup()

# 执行操作，自动处理弹窗
automation.click(500, 300)  # 点击坐标
automation.input_text("测试文本")  # 输入文本
automation.click_element({'resourceId': 'com.example:id/button'})  # 点击元素
```

## 详细使用方法

### 1. PopupTool 类

#### 初始化

```python
import uiautomator2 as u2
from core.popup_tool import PopupTool

# 使用默认设备
popup_tool = PopupTool()

# 使用指定设备
device = u2.connect("device_id")
popup_tool = PopupTool(device)
```

#### 主要方法

##### detect_and_close_popup()

检测并关闭弹窗的主要方法。

```python
# 基本使用
success = popup_tool.detect_and_close_popup()

# 自定义超时时间和检查间隔
success = popup_tool.detect_and_close_popup(timeout=15, check_interval=1.0)
```

参数：
- `timeout`: 超时时间（秒），默认 10 秒
- `check_interval`: 检查间隔（秒），默认 0.5 秒

##### is_popup_present()

检测当前是否有弹窗存在。

```python
if popup_tool.is_popup_present():
    print("检测到弹窗")
    popup_tool.detect_and_close_popup()
```

##### safe_action()

为任意操作添加弹窗处理。

```python
def my_operation():
    # 执行一些操作
    device.click(100, 200)
    device.send_keys("hello")
    return "完成"

# 在操作前后自动处理弹窗
result = popup_tool.safe_action(my_operation)
```

##### 便捷操作方法

```python
# 带弹窗处理的点击
popup_tool.click_with_popup_handling(x=500, y=300)

# 带弹窗处理的输入
popup_tool.input_with_popup_handling("测试文本")

# 带弹窗处理的元素点击
popup_tool.element_click_with_popup_handling({
    'resourceId': 'com.example:id/button'
})
```

### 2. AutomationWithPopupTool 类

提供更简洁的自动化操作接口。

```python
from core.popup_tool import AutomationWithPopupTool

automation = AutomationWithPopupTool()

# 所有操作都自动处理弹窗
automation.click(500, 300)
automation.input_text("测试文本")
automation.click_element({'text': '确定'})
automation.detect_and_close_popup()
automation.is_popup_present()
```

## 配置说明

### 预定义的关闭按钮文本

工具预定义了常见的关闭按钮文本：

```python
close_texts = [
    '关闭', '取消', '确定', '同意', '知道了', '我知道了',
    '立即体验', '稍后再说', '跳过', '继续', '允许', '拒绝',
    '好的', '明白', '下次再说', '暂不需要', '以后再说',
    'X', '×', '✕', 'Close', 'OK', 'Cancel', 'Skip',
    'Allow', 'Deny', 'Later', 'Continue', 'Got it',
    'Dismiss', 'Accept', 'Decline', 'Not now'
]
```

### 预定义的资源 ID

```python
close_ids = [
    'android:id/button1',  # 系统对话框确定按钮
    'android:id/button2',  # 系统对话框取消按钮
    'android:id/button3',  # 系统对话框第三个按钮
    'com.android.packageinstaller:id/permission_allow_button',
    'com.android.packageinstaller:id/permission_deny_button',
    'android:id/aerr_close',  # ANR对话框关闭按钮
    'android:id/aerr_restart',  # ANR对话框重启按钮
]
```

### 弹窗类型检测

```python
popup_classes = [
    'android.app.Dialog',
    'android.app.AlertDialog',
    'android.widget.PopupWindow',
    'android.support.v7.app.AlertDialog',
    'androidx.appcompat.app.AlertDialog'
]
```

## 实际应用示例

### 示例 1: 应用启动时处理权限弹窗

```python
from core.popup_tool import create_automation_with_popup

def launch_app_with_permission_handling():
    automation = create_automation_with_popup()
    
    # 启动应用
    automation.device.app_start("com.example.app")
    
    # 等待应用启动并处理可能的权限弹窗
    time.sleep(3)
    automation.detect_and_close_popup(timeout=10)
    
    # 继续后续操作
    automation.click_element({'text': '开始使用'})
```

### 示例 2: 登录流程中的弹窗处理

```python
def login_with_popup_handling(username, password):
    automation = create_automation_with_popup()
    
    # 输入用户名
    automation.click_element({'resourceId': 'com.example:id/username'})
    automation.input_text(username)
    
    # 输入密码
    automation.click_element({'resourceId': 'com.example:id/password'})
    automation.input_text(password)
    
    # 点击登录按钮
    automation.click_element({'resourceId': 'com.example:id/login_btn'})
    
    # 等待登录结果，处理可能的弹窗
    time.sleep(2)
    automation.detect_and_close_popup(timeout=5)
```

### 示例 3: 批量操作中的弹窗处理

```python
def batch_operation_with_popup_handling():
    popup_tool = create_popup_tool()
    
    operations = [
        lambda: popup_tool.device.click(100, 200),
        lambda: popup_tool.device.click(200, 300),
        lambda: popup_tool.device.click(300, 400),
    ]
    
    for operation in operations:
        # 每个操作都使用safe_action包装
        try:
            popup_tool.safe_action(operation)
            print("操作成功")
        except Exception as e:
            print(f"操作失败: {e}")
        
        time.sleep(1)
```

## 注意事项

1. **超时设置**: 根据应用响应速度合理设置超时时间
2. **检查间隔**: 频繁检查可能影响性能，建议使用默认值
3. **日志监控**: 关注日志输出，了解弹窗处理情况
4. **异常处理**: 在关键流程中添加异常处理逻辑
5. **设备兼容性**: 不同设备和系统版本可能有差异

## 与现有系统的集成

如果项目中已有其他弹窗处理系统，可以这样集成：

```python
from core.popup_tool import create_popup_tool
from core.popup_monitor import PopupMonitor  # 现有系统

def hybrid_popup_handling():
    # 使用简化工具进行快速处理
    popup_tool = create_popup_tool()
    
    if popup_tool.is_popup_present():
        # 先尝试简化工具
        if not popup_tool.detect_and_close_popup(timeout=5):
            # 如果失败，使用完整的监控系统
            monitor = PopupMonitor(popup_tool.device)
            monitor.handle_popup_immediately()
```

## 故障排除

### 常见问题

1. **弹窗检测不到**: 检查弹窗类型是否在预定义列表中
2. **关闭按钮找不到**: 添加自定义文本或资源 ID
3. **操作超时**: 增加超时时间或检查网络连接
4. **设备连接失败**: 确认设备连接和 UIAutomator2 服务状态

### 调试技巧

```python
# 启用详细日志
import logging
logging.getLogger('core.popup_tool').setLevel(logging.DEBUG)

# 检查当前屏幕状态
popup_tool = create_popup_tool()
print(f"弹窗存在: {popup_tool.is_popup_present()}")
print(f"屏幕信息: {popup_tool.device.info}")
```
