import uiautomator2 as u2
from paddleocr import PaddleOCR
import cv2
import numpy as np
import time


class PopupHandler:
    def __init__(self, device_id=None):
        self.device = u2.connect(device_id)
        self.ocr = PaddleOCR(use_angle_cls=True, lang='ch')

        # 常见的关闭按钮文本
        self.close_texts = [
            '关闭', '取消', '确定', '同意', '知道了', '我知道了',
            '立即体验', '稍后再说', '跳过', '继续', '允许', '拒绝',
            'X', '×', '✕', 'Close', 'OK', 'Cancel', 'Skip',
            'Allow', 'Deny', 'Later', 'Continue', 'Got it'
        ]

        # 常见的关闭按钮resource-id
        self.close_ids = [
            'android:id/button1',  # 系统对话框确定按钮
            'android:id/button2',  # 系统对话框取消按钮
            'com.android.packageinstaller:id/permission_allow_button',
            'com.android.packageinstaller:id/permission_deny_button',
        ]

    def detect_and_close_popup(self, timeout=10):
        """检测并关闭弹窗"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            # 1. 首先尝试通过UI层次结构查找
            if self._try_close_by_ui_hierarchy():
                return True

            # 2. 如果UI层次结构找不到，使用OCR
            if self._try_close_by_ocr():
                return True

            # 3. 最后尝试图像模板匹配
            if self._try_close_by_template():
                return True

            time.sleep(0.5)

        return False

    def _try_close_by_ui_hierarchy(self):
        """通过UI层次结构查找关闭按钮"""
        try:
            # 1. 通过resource-id查找
            for res_id in self.close_ids:
                element = self.device(resourceId=res_id)
                if element.exists:
                    element.click()
                    time.sleep(0.5)
                    return True

            # 2. 通过文本查找
            for text in self.close_texts:
                element = self.device(text=text)
                if element.exists:
                    element.click()
                    time.sleep(0.5)
                    return True

                # 也尝试包含匹配
                element = self.device(textContains=text)
                if element.exists:
                    element.click()
                    time.sleep(0.5)
                    return True

            # 3. 通过className查找常见的关闭按钮
            close_buttons = self.device(className="android.widget.ImageButton")
            for button in close_buttons:
                if button.exists:
                    # 检查是否在屏幕右上角（常见关闭按钮位置）
                    bounds = button.info['bounds']
                    screen_width = self.device.info['displayWidth']
                    if bounds['right'] > screen_width * 0.8:  # 右侧80%区域
                        button.click()
                        time.sleep(0.5)
                        return True

        except Exception as e:
            print(f"UI层次结构检测失败: {e}")

        return False

    def _try_close_by_ocr(self):
        """使用OCR识别关闭按钮"""
        try:
            # 截取当前屏幕
            screenshot = self.device.screenshot(format='opencv')

            # 使用OCR识别文本
            results = self.ocr.ocr(screenshot, cls=True)

            if not results or not results[0]:
                return False

            for line in results[0]:
                text = line[1][0].strip()
                confidence = line[1][1]

                # 只处理高置信度的文本
                if confidence < 0.8:
                    continue

                # 检查是否是关闭相关的文本
                if any(close_text in text for close_text in self.close_texts):
                    # 获取文本框坐标
                    box = line[0]

                    # 计算中心点
                    center_x = int(sum([point[0] for point in box]) / 4)
                    center_y = int(sum([point[1] for point in box]) / 4)

                    # 点击
                    self.device.click(center_x, center_y)
                    time.sleep(0.5)
                    return True

        except Exception as e:
            print(f"OCR检测失败: {e}")

        return False

    def _try_close_by_template(self):
        """使用模板匹配查找关闭按钮"""
        try:
            # 截取当前屏幕
            screenshot = self.device.screenshot(format='opencv')

            # 预定义的关闭按钮模板（你需要准备这些模板图片）
            templates = [
                'templates/close_x.png',
                'templates/close_button.png',
                'templates/cancel_button.png',
                # 添加更多模板...
            ]

            for template_path in templates:
                try:
                    template = cv2.imread(template_path)
                    if template is None:
                        continue

                    # 模板匹配
                    result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
                    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                    # 如果匹配度足够高
                    if max_val > 0.8:
                        # 计算模板中心点
                        h, w = template.shape[:2]
                        center_x = max_loc[0] + w // 2
                        center_y = max_loc[1] + h // 2

                        # 点击
                        self.device.click(center_x, center_y)
                        time.sleep(0.5)
                        return True

                except Exception as e:
                    print(f"模板匹配失败 {template_path}: {e}")
                    continue

        except Exception as e:
            print(f"图像模板匹配失败: {e}")

        return False

    def is_popup_present(self):
        """检测是否有弹窗出现"""
        try:
            # 方法1: 检查是否有Dialog类型的窗口
            dialogs = self.device(className="android.app.Dialog")
            if dialogs.exists:
                return True

            # 方法2: 检查是否有AlertDialog
            alert_dialogs = self.device(className="android.app.AlertDialog")
            if alert_dialogs.exists:
                return True

            # 方法3: 检查屏幕中央是否有悬浮的视图
            screen_width = self.device.info['displayWidth']
            screen_height = self.device.info['displayHeight']

            # 检查屏幕中央区域是否有可点击元素
            center_elements = self.device(
                clickable=True,
                bounds=(
                    screen_width * 0.2, screen_height * 0.2,
                    screen_width * 0.8, screen_height * 0.8
                )
            )

            if center_elements.exists:
                return True

        except Exception as e:
            print(f"弹窗检测失败: {e}")

        return False


class AutomationWithPopupHandler:
    def __init__(self, device_id=None):
        self.device = u2.connect(device_id)
        self.popup_handler = PopupHandler(device_id)

    def safe_action(self, action_func, *args, **kwargs):
        """执行操作前后检查并处理弹窗"""
        # 执行操作前检查弹窗
        self.popup_handler.detect_and_close_popup(timeout=3)

        # 执行主要操作
        result = action_func(*args, **kwargs)

        # 执行操作后再次检查弹窗
        self.popup_handler.detect_and_close_popup(timeout=5)

        return result

    def click_with_popup_handling(self, x, y):
        """带弹窗处理的点击"""
        return self.safe_action(self.device.click, x, y)

    def input_with_popup_handling(self, text):
        """带弹窗处理的输入"""
        return self.safe_action(self.device.send_keys, text)


# 使用示例
automation = AutomationWithPopupHandler()

# 执行自动化操作，自动处理弹窗
automation.click_with_popup_handling(500, 300)
automation.input_with_popup_handling("测试文本")
