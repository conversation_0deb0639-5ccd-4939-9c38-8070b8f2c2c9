"""
文件选择器调试工具
用于分析文件选择页面的元素结构，帮助优化选择逻辑
"""
import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import log
from core.base_driver import driver_manager


class FileSelectorDebugger:
    """文件选择器调试工具"""

    def __init__(self):
        """初始化调试工具"""
        self.driver_manager = driver_manager
        self.driver = None
    
    def start_debugging(self):
        """开始调试"""
        try:
            log.info("启动文件选择器调试工具")

            # 获取驱动实例
            self.driver = self.driver_manager.driver
            log.info("✅ 驱动初始化成功")

            return True

        except Exception as e:
            log.error(f"启动调试工具失败: {e}")
            return False
    
    def analyze_current_page(self) -> dict:
        """分析当前页面结构"""
        try:
            log.info("开始分析当前页面结构")
            
            page_info = {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "current_app": self._get_current_app(),
                "page_source": None,
                "elements": {
                    "text_views": [],
                    "image_views": [],
                    "buttons": [],
                    "recycler_views": [],
                    "linear_layouts": [],
                    "clickable_elements": []
                }
            }
            
            # 获取页面源码
            try:
                page_info["page_source"] = self.driver.dump_hierarchy()
            except Exception as e:
                log.warning(f"获取页面源码失败: {e}")
            
            # 分析各种元素
            self._analyze_text_views(page_info["elements"]["text_views"])
            self._analyze_image_views(page_info["elements"]["image_views"])
            self._analyze_buttons(page_info["elements"]["buttons"])
            self._analyze_recycler_views(page_info["elements"]["recycler_views"])
            self._analyze_linear_layouts(page_info["elements"]["linear_layouts"])
            self._analyze_clickable_elements(page_info["elements"]["clickable_elements"])
            
            return page_info
            
        except Exception as e:
            log.error(f"分析页面结构失败: {e}")
            return {}
    
    def _get_current_app(self) -> str:
        """获取当前应用包名"""
        try:
            current_app = self.driver.app_current()
            return current_app.get('package', 'unknown')
        except:
            return 'unknown'
    
    def _analyze_text_views(self, text_views: list):
        """分析TextView元素"""
        try:
            elements = self.driver(className="android.widget.TextView")
            if elements.exists():
                for i in range(min(len(elements), 20)):  # 最多分析20个
                    try:
                        element = elements[i]
                        info = {
                            "index": i,
                            "text": element.get_text(),
                            "resource_id": element.info.get('resourceName', ''),
                            "bounds": element.info.get('bounds', {}),
                            "clickable": element.info.get('clickable', False),
                            "enabled": element.info.get('enabled', True)
                        }
                        text_views.append(info)
                    except:
                        continue
        except Exception as e:
            log.warning(f"分析TextView失败: {e}")
    
    def _analyze_image_views(self, image_views: list):
        """分析ImageView元素"""
        try:
            elements = self.driver(className="android.widget.ImageView")
            if elements.exists():
                for i in range(min(len(elements), 15)):  # 最多分析15个
                    try:
                        element = elements[i]
                        bounds = element.info.get('bounds', {})
                        width = bounds.get('right', 0) - bounds.get('left', 0)
                        height = bounds.get('bottom', 0) - bounds.get('top', 0)
                        
                        info = {
                            "index": i,
                            "resource_id": element.info.get('resourceName', ''),
                            "bounds": bounds,
                            "width": width,
                            "height": height,
                            "clickable": element.info.get('clickable', False),
                            "content_desc": element.info.get('contentDescription', '')
                        }
                        image_views.append(info)
                    except:
                        continue
        except Exception as e:
            log.warning(f"分析ImageView失败: {e}")
    
    def _analyze_buttons(self, buttons: list):
        """分析Button元素"""
        try:
            elements = self.driver(className="android.widget.Button")
            if elements.exists():
                for i in range(len(elements)):
                    try:
                        element = elements[i]
                        info = {
                            "index": i,
                            "text": element.get_text(),
                            "resource_id": element.info.get('resourceName', ''),
                            "bounds": element.info.get('bounds', {}),
                            "enabled": element.info.get('enabled', True)
                        }
                        buttons.append(info)
                    except:
                        continue
        except Exception as e:
            log.warning(f"分析Button失败: {e}")
    
    def _analyze_recycler_views(self, recycler_views: list):
        """分析RecyclerView元素"""
        try:
            elements = self.driver(className="androidx.recyclerview.widget.RecyclerView")
            if elements.exists():
                for i in range(len(elements)):
                    try:
                        element = elements[i]
                        children = element.child()
                        child_count = len(children) if children.exists() else 0
                        
                        info = {
                            "index": i,
                            "resource_id": element.info.get('resourceName', ''),
                            "bounds": element.info.get('bounds', {}),
                            "child_count": child_count
                        }
                        recycler_views.append(info)
                    except:
                        continue
        except Exception as e:
            log.warning(f"分析RecyclerView失败: {e}")
    
    def _analyze_linear_layouts(self, linear_layouts: list):
        """分析LinearLayout元素"""
        try:
            elements = self.driver(className="android.widget.LinearLayout")
            if elements.exists():
                for i in range(min(len(elements), 10)):  # 最多分析10个
                    try:
                        element = elements[i]
                        has_text_child = element.child(className="android.widget.TextView").exists()
                        has_image_child = element.child(className="android.widget.ImageView").exists()
                        
                        info = {
                            "index": i,
                            "resource_id": element.info.get('resourceName', ''),
                            "bounds": element.info.get('bounds', {}),
                            "clickable": element.info.get('clickable', False),
                            "has_text_child": has_text_child,
                            "has_image_child": has_image_child
                        }
                        linear_layouts.append(info)
                    except:
                        continue
        except Exception as e:
            log.warning(f"分析LinearLayout失败: {e}")
    
    def _analyze_clickable_elements(self, clickable_elements: list):
        """分析所有可点击元素"""
        try:
            elements = self.driver(clickable=True)
            if elements.exists():
                for i in range(min(len(elements), 15)):  # 最多分析15个
                    try:
                        element = elements[i]
                        info = {
                            "index": i,
                            "class_name": element.info.get('className', ''),
                            "text": element.get_text() if hasattr(element, 'get_text') else '',
                            "resource_id": element.info.get('resourceName', ''),
                            "bounds": element.info.get('bounds', {}),
                            "content_desc": element.info.get('contentDescription', '')
                        }
                        clickable_elements.append(info)
                    except:
                        continue
        except Exception as e:
            log.warning(f"分析可点击元素失败: {e}")
    
    def save_analysis_result(self, page_info: dict, filename: str = None):
        """保存分析结果"""
        try:
            if not filename:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"file_selector_analysis_{timestamp}.json"
            
            output_dir = project_root / "reports" / "debug_reports"
            output_dir.mkdir(parents=True, exist_ok=True)
            
            output_file = output_dir / filename
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(page_info, f, ensure_ascii=False, indent=2)
            
            log.info(f"✅ 分析结果已保存到: {output_file}")
            return str(output_file)
            
        except Exception as e:
            log.error(f"保存分析结果失败: {e}")
            return None
    
    def print_summary(self, page_info: dict):
        """打印分析摘要"""
        try:
            print("\n" + "="*60)
            print("📊 文件选择器页面分析摘要")
            print("="*60)
            
            print(f"🕒 分析时间: {page_info.get('timestamp', 'unknown')}")
            print(f"📱 当前应用: {page_info.get('current_app', 'unknown')}")
            
            elements = page_info.get('elements', {})
            
            print(f"\n📋 元素统计:")
            print(f"  • TextView: {len(elements.get('text_views', []))}")
            print(f"  • ImageView: {len(elements.get('image_views', []))}")
            print(f"  • Button: {len(elements.get('buttons', []))}")
            print(f"  • RecyclerView: {len(elements.get('recycler_views', []))}")
            print(f"  • LinearLayout: {len(elements.get('linear_layouts', []))}")
            print(f"  • 可点击元素: {len(elements.get('clickable_elements', []))}")
            
            # 显示可能的文件项
            print(f"\n📄 可能的文件项 (TextView):")
            text_views = elements.get('text_views', [])
            for tv in text_views[:10]:  # 显示前10个
                text = tv.get('text', '')
                if text and len(text) > 0:
                    print(f"  • {text[:50]}{'...' if len(text) > 50 else ''}")
            
            # 显示确认按钮
            print(f"\n🔘 可能的确认按钮:")
            buttons = elements.get('buttons', [])
            for btn in buttons:
                text = btn.get('text', '')
                if text:
                    print(f"  • {text}")
            
            print("="*60)
            
        except Exception as e:
            log.error(f"打印摘要失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.driver_manager:
                self.driver_manager.quit()
                log.info("✅ 驱动已清理")
        except Exception as e:
            log.warning(f"清理资源时出错: {e}")


def main():
    """主函数"""
    debugger = FileSelectorDebugger()
    
    try:
        # 启动调试工具
        if not debugger.start_debugging():
            print("❌ 调试工具启动失败")
            return
        
        print("🔍 文件选择器调试工具已启动")
        print("请手动导航到文件选择页面，然后按回车键开始分析...")
        input()
        
        # 分析当前页面
        page_info = debugger.analyze_current_page()
        
        if page_info:
            # 打印摘要
            debugger.print_summary(page_info)
            
            # 保存详细结果
            output_file = debugger.save_analysis_result(page_info)
            if output_file:
                print(f"\n📁 详细分析结果已保存到: {output_file}")
        else:
            print("❌ 页面分析失败")
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断调试")
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
    finally:
        debugger.cleanup()


if __name__ == "__main__":
    main()
