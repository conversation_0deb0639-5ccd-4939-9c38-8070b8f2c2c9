#!/usr/bin/env python3
"""
从 process_cleanup_config.json 中移除系统应用，只保留独立APK应用
"""

import json
from pathlib import Path
from typing import List, Dict

def is_independent_app(package_name: str, category: str) -> bool:
    """判断是否为独立APK应用"""
    
    # 明确的独立应用包名
    independent_apps = {
        # 社交应用
        'com.facebook.katana',
        'com.facebook.lite', 
        'com.whatsapp',
        'com.instagram.android',
        'com.twitter.android',
        'com.tencent.mm',
        'com.alibaba.android.rimet',
        'com.tencent.mobileqq',
        'com.sina.weibo',
        
        # 购物应用
        'com.taobao.taobao',
        'com.jingdong.app.mall',
        
        # 游戏应用
        'com.tencent.tmgp.sgame',
        'com.tencent.ig',
        
        # 媒体应用
        'com.ss.android.ugc.aweme',
        'com.smile.gifmaker',
        'com.spotify.music',
        'com.netflix.mediaclient',
        'com.netease.cloudmusic',
        'com.tencent.qqmusic',
        'com.kugou.android',
        'com.kuwo.kwmusic',
        
        # 浏览器
        'com.android.chrome',
        'com.UCMobile',
        
        # 地图应用
        'com.google.android.apps.maps',
        'com.autonavi.minimap',
        'com.baidu.BaiduMap',
        'com.sogou.map.android.maps',
        'com.tencent.map',
        
        # 工具应用
        'com.accuweather',
        'com.weather.forecast',
        'com.weather.channel',
        
        # 视频编辑
        'com.aigallery.videoeditor',
        
        # YouTube
        'com.google.android.youtube',
    }
    
    # 如果在独立应用列表中，直接返回True
    if package_name in independent_apps:
        return True
    
    # 系统应用包名特征（这些都应该被移除）
    system_patterns = [
        'com.android.',
        'com.transsion.',
        'com.sh.smart.',
        'com.google.android.gms',
        'com.google.android.apps.restore',
        'com.google.android.apps.messaging',
        'com.google.android.contacts',
        'com.google.android.dialer',
        'com.google.android.documentsui',
        'com.google.android.GoogleCamera',
        'com.google.android.deskclock',
        'com.google.android.music',
        'com.google.android.apps.nexuslauncher',
        'com.google.android.apps.weather',
        'com.samsung.android.',
        'com.sec.android.',
        'com.huawei.',
        'com.xiaomi.',
        'com.oppo.',
        'com.vivo.',
        'com.miui.',
        'com.oneplus.',
        'com.coloros.',
        'com.es.android.filemanager',
        'com.gallery20',
        'com.funbase.xradio',
        'com.hoffnung',
        'com.idea.questionnaire',
    ]
    
    # 检查是否为系统应用
    for pattern in system_patterns:
        if package_name.startswith(pattern):
            return False
    
    # 如果分类为system_app，也认为是系统应用
    if category == "system_app":
        return False
    
    # 其他情况认为是独立应用
    return True

def cleanup_config_file():
    """清理配置文件，移除系统应用"""
    config_file = Path("config/process_cleanup_config.json")
    
    # 读取现有配置
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 获取原始应用列表
    original_apps = config['process_cleanup_config']['common_user_apps']
    original_count = len(original_apps)
    
    # 过滤出独立应用
    independent_apps = []
    removed_apps = []
    
    for app in original_apps:
        package_name = app['package']
        category = app['category']
        
        if is_independent_app(package_name, category):
            independent_apps.append(app)
        else:
            removed_apps.append(app)
    
    # 更新配置
    config['process_cleanup_config']['common_user_apps'] = independent_apps
    
    # 写回配置文件
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    # 输出统计信息
    print(f"🧹 配置文件清理完成")
    print(f"   - 原始应用数量: {original_count}")
    print(f"   - 保留独立应用: {len(independent_apps)}")
    print(f"   - 移除系统应用: {len(removed_apps)}")
    
    # 显示保留的应用
    print(f"\n✅ 保留的独立应用 ({len(independent_apps)} 个):")
    for app in independent_apps:
        print(f"   - {app['package']} ({app['description']}) [{app['category']}]")
    
    # 显示移除的应用分类统计
    print(f"\n❌ 移除的系统应用分类统计:")
    category_count = {}
    for app in removed_apps:
        category = app['category']
        category_count[category] = category_count.get(category, 0) + 1
    
    for category, count in sorted(category_count.items()):
        print(f"   - {category}: {count} 个")

def main():
    """主函数"""
    print("🔍 开始清理系统应用...")
    
    try:
        cleanup_config_file()
        print(f"\n🎉 清理完成！配置文件已更新。")
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
