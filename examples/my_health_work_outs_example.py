"""
My Health Work Outs页面使用示例
参考Ella的dialogue_page实现模式
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from pages.apps.my_health.work_outs_page import MyHealthWorkOutsPage
from core.logger import log


def main():
    """主函数 - 演示Work Outs页面的各种功能"""
    
    # 初始化页面对象
    work_outs_page = MyHealthWorkOutsPage()
    
    try:
        log.info("🚀 开始My Health Work Outs页面功能演示")
        
        # 1. 启动应用
        log.info("1. 启动My Health应用...")
        if not work_outs_page.start_app():
            log.error("❌ 应用启动失败")
            return False
        
        # 2. 等待页面加载
        log.info("2. 等待页面加载...")
        if not work_outs_page.wait_for_page_load():
            log.error("❌ 页面加载失败")
            return False
        
        # 3. 导航到Work Outs页面
        log.info("3. 导航到Work Outs页面...")
        if not work_outs_page.navigate_to_work_outs():
            log.warning("⚠️ 可能已经在Work Outs页面")
        
        # 4. 确保在Work Outs页面
        log.info("4. 确保在Work Outs页面...")
        if not work_outs_page.ensure_on_work_outs_page():
            log.error("❌ 无法确保在Work Outs页面")
            return False
        
        # 5. 获取可用分类
        log.info("5. 获取可用的锻炼分类...")
        categories = work_outs_page.get_available_categories()
        log.info(f"✅ 找到分类: {categories}")
        
        # 6. 选择分类
        if categories:
            log.info("6. 选择第一个分类...")
            if work_outs_page.select_category(categories[0]):
                log.info(f"✅ 成功选择分类: {categories[0]}")
            else:
                log.warning("⚠️ 选择分类失败")
        
        # 7. 获取锻炼数量
        log.info("7. 获取锻炼数量...")
        workout_count = work_outs_page.get_workout_count()
        log.info(f"✅ 找到 {workout_count} 个锻炼")
        
        # 8. 获取锻炼列表
        log.info("8. 获取锻炼列表...")
        workouts = work_outs_page.get_workout_list()
        if workouts:
            log.info(f"✅ 获取到 {len(workouts)} 个锻炼:")
            for i, workout in enumerate(workouts[:3]):  # 只显示前3个
                log.info(f"   {i+1}. {workout['title']}")
        
        # 9. 测试搜索功能
        log.info("9. 测试搜索功能...")
        if work_outs_page.search_workout("yoga"):
            log.info("✅ 搜索功能测试成功")
            
            # 清空搜索
            work_outs_page.clear_search()
            log.info("✅ 搜索已清空")
        else:
            log.warning("⚠️ 搜索功能测试失败")
        
        # 10. 测试滚动功能
        log.info("10. 测试滚动功能...")
        if work_outs_page.scroll_to_bottom():
            log.info("✅ 滚动到底部成功")
        
        if work_outs_page.scroll_to_top():
            log.info("✅ 滚动到顶部成功")
        
        # 11. 测试刷新功能
        log.info("11. 测试刷新功能...")
        if work_outs_page.refresh_page():
            log.info("✅ 页面刷新成功")
        else:
            log.warning("⚠️ 页面刷新失败")
        
        # 12. 选择并开始锻炼（如果有锻炼可用）
        if workouts:
            log.info("12. 选择第一个锻炼...")
            if work_outs_page.select_workout(0):
                log.info("✅ 锻炼选择成功")
                
                # 注意：这里不实际开始锻炼，只是演示功能
                log.info("💡 可以调用 start_workout() 来开始锻炼")
            else:
                log.warning("⚠️ 锻炼选择失败")
        
        # 13. 检查应用状态
        log.info("13. 检查My Health应用状态...")
        if work_outs_page.check_my_health_app_opened():
            log.info("✅ My Health应用正在运行")
        else:
            log.warning("⚠️ My Health应用可能未运行")
        
        log.info("🎉 Work Outs页面功能演示完成！")
        return True
        
    except Exception as e:
        log.error(f"❌ 演示过程中发生异常: {e}")
        return False
    
    finally:
        # 清理：返回主页
        log.info("🧹 清理：返回主页...")
        work_outs_page.navigate_to_home()


def test_comprehensive():
    """运行综合测试"""
    log.info("🧪 运行Work Outs页面综合测试...")
    
    work_outs_page = MyHealthWorkOutsPage()
    result = work_outs_page.perform_comprehensive_test()
    
    if result:
        log.info("✅ 综合测试通过")
    else:
        log.error("❌ 综合测试失败")
    
    return result


def test_specific_features():
    """测试特定功能"""
    log.info("🔧 测试特定功能...")
    
    work_outs_page = MyHealthWorkOutsPage()
    
    # 启动应用
    if not work_outs_page.start_app():
        log.error("❌ 应用启动失败")
        return False
    
    # 测试页面状态检查
    log.info("测试页面状态检查...")
    if work_outs_page._check_work_outs_page_indicators():
        log.info("✅ 页面指示器检查通过")
    else:
        log.warning("⚠️ 页面指示器检查失败")
    
    # 测试错误处理
    log.info("测试错误处理...")
    work_outs_page.handle_network_error()
    
    # 测试空状态检查
    log.info("测试空状态检查...")
    if work_outs_page.check_empty_state():
        log.info("✅ 检测到空状态")
    else:
        log.info("✅ 没有空状态（正常）")
    
    # 测试加载状态检查
    log.info("测试加载状态检查...")
    if work_outs_page.check_loading_state():
        log.info("✅ 检测到加载状态")
    else:
        log.info("✅ 没有加载状态（正常）")
    
    return True


if __name__ == "__main__":
    print("=" * 60)
    print("My Health Work Outs页面功能演示")
    print("=" * 60)
    
    # 选择运行模式
    mode = input("请选择运行模式:\n1. 完整功能演示\n2. 综合测试\n3. 特定功能测试\n请输入数字 (1-3): ").strip()
    
    if mode == "1":
        main()
    elif mode == "2":
        test_comprehensive()
    elif mode == "3":
        test_specific_features()
    else:
        print("运行默认的完整功能演示...")
        main()
    
    print("=" * 60)
    print("演示结束")
    print("=" * 60)
